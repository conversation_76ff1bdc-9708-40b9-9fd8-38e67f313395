# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class WidgetTextContents(UncheckedBaseModel):
    main_label: typing.Optional[str] = pydantic.Field(default=None)
    """
    Call to action displayed inside the compact and full variants.
    """

    start_call: typing.Optional[str] = pydantic.Field(default=None)
    """
    Text and ARIA label for the start call button.
    """

    new_call: typing.Optional[str] = pydantic.Field(default=None)
    """
    Text and ARIA label for the new call button. Displayed when the caller already finished at least one call in order ot start the next one.
    """

    end_call: typing.Optional[str] = pydantic.Field(default=None)
    """
    Text and ARIA label for the end call button.
    """

    mute_microphone: typing.Optional[str] = pydantic.Field(default=None)
    """
    ARIA label for the mute microphone button.
    """

    change_language: typing.Optional[str] = pydantic.Field(default=None)
    """
    ARIA label for the change language dropdown.
    """

    collapse: typing.Optional[str] = pydantic.Field(default=None)
    """
    ARIA label for the collapse button.
    """

    expand: typing.Optional[str] = pydantic.Field(default=None)
    """
    ARIA label for the expand button.
    """

    copied: typing.Optional[str] = pydantic.Field(default=None)
    """
    Text displayed when the user copies a value using the copy button.
    """

    accept_terms: typing.Optional[str] = pydantic.Field(default=None)
    """
    Text and ARIA label for the accept terms button.
    """

    dismiss_terms: typing.Optional[str] = pydantic.Field(default=None)
    """
    Text and ARIA label for the cancel terms button.
    """

    listening_status: typing.Optional[str] = pydantic.Field(default=None)
    """
    Status displayed when the agent is listening.
    """

    speaking_status: typing.Optional[str] = pydantic.Field(default=None)
    """
    Status displayed when the agent is speaking.
    """

    connecting_status: typing.Optional[str] = pydantic.Field(default=None)
    """
    Status displayed when the agent is connecting.
    """

    input_label: typing.Optional[str] = pydantic.Field(default=None)
    """
    ARIA label for the text message input.
    """

    input_placeholder: typing.Optional[str] = pydantic.Field(default=None)
    """
    Placeholder text for the text message input.
    """

    user_ended_conversation: typing.Optional[str] = pydantic.Field(default=None)
    """
    Information message displayed when the user ends the conversation.
    """

    agent_ended_conversation: typing.Optional[str] = pydantic.Field(default=None)
    """
    Information message displayed when the agent ends the conversation.
    """

    conversation_id: typing.Optional[str] = pydantic.Field(default=None)
    """
    Text label used next to the conversation ID.
    """

    error_occurred: typing.Optional[str] = pydantic.Field(default=None)
    """
    Text label used when an error occurs.
    """

    copy_id: typing.Optional[str] = pydantic.Field(default=None)
    """
    Text and ARIA label used for the copy ID button.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow

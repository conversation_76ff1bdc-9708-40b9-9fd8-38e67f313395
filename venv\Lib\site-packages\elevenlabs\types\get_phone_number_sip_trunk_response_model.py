# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .phone_number_agent_info import PhoneNumberAgentInfo
from .sip_trunk_config_response_model import SipTrunkConfigResponseModel


class GetPhoneNumberSipTrunkResponseModel(UncheckedBaseModel):
    phone_number: str = pydantic.Field()
    """
    Phone number
    """

    label: str = pydantic.Field()
    """
    Label for the phone number
    """

    phone_number_id: str = pydantic.Field()
    """
    The ID of the phone number
    """

    assigned_agent: typing.Optional[PhoneNumberAgentInfo] = pydantic.Field(default=None)
    """
    The agent that is assigned to the phone number
    """

    provider_config: typing.Optional[SipTrunkConfigResponseModel] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow

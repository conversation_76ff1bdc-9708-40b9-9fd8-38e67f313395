import json
import os

from moviepy import AudioFileClip, CompositeAudioClip

from utils.asyncNarakeet import narak<PERSON>t<PERSON>un
from utils.audioSeparation import audioSeparation
from utils.elenvenlabs import audio_clip, CloneVideo, create_el_run


async def generate_clone_audio(task_id, target_languages):
	from app import redis_client
	# 更新任务进度 --- 4
	task_dict = json.loads(redis_client.get(task_id))
	task_dict["status"]["code"] = "4"
	task_dict["status"]["statu"] = "pending"
	try:
		redis_client.set(task_id, json.dumps(task_dict, ensure_ascii=False))
	except Exception as e:
		return {
			"code": 500,
			"status": f"task 4 status update failed reason is {e}",
		}

	# 任务4 --- 校验translate.vtt 文件是否存在
	# translate_vtt_path = f"./allData/output/{task_id}/translate.vtt"
	# if not os.path.exists(translate_vtt_path):
	# 	task_dict["status"]["statu"] = "fail"
	# 	try:
	# 		redis_client.set(task_id, json.dumps(task_dict, ensure_ascii=False))
	# 	except Exception as e:
	# 		return {
	# 			"code": 500,
	# 			"status": f"task 4 error status update failed reason is {e}",
	# 		}
	# 	return "translate vtt file is not found"
	#
	# # 校验vtt文件是否以WEBVTT开头、如果不则添加
	# with open(f"./allData/output/{task_id}/translate.vtt", 'r+', encoding='utf-8') as file:
	# 	lines = file.readlines()
	# 	modified = False  # Track if changes are made
	#
	# 	# Check if the file is empty or the first line isn't "WEBVTT"
	# 	if not lines or lines[0].strip() != "WEBVTT":
	# 		print("Error: The file does not start with 'WEBVTT'. Attempting to fix...")
	# 		# Prepend "WEBVTT" followed by a newline and ensure an empty line if needed
	# 		header = ["WEBVTT\n", "\n"]  # Some systems expect a blank line after
	# 		# Check if there's existing content to avoid double blank lines
	# 		if lines and lines[0].strip() == "":
	# 			lines[0:0] = ["WEBVTT\n"]
	# 		else:
	# 			lines[0:0] = header
	# 		modified = True
	#
	# 	if modified:
	# 		# Write changes back to the file
	# 		file.seek(0)
	# 		file.writelines(lines)
	# 		file.truncate()  # Remove any leftover content
	# 		print("修改完毕")
	#
	# try:
	# 	with open(f"./allData/output/{task_id}/translate.vtt", 'r', encoding='utf-8') as f:
	# 		data = f.read()
	# 	await narakeetRun("vtt", task_id, target_languages, data=data)
	# except Exception as e:
	# 	task_dict["status"]["statu"] = "fail"
	# 	try:
	# 		redis_client.set(task_id, json.dumps(task_dict, ensure_ascii=False))
	# 	except Exception as e:
	# 		return {
	# 			"code": 500,
	# 			"status": f"task 4 Narakeet error status update failed reason is {e}",
	# 		}

	try:
		audioSeparation(task_id, "2track_vocal")
	except Exception as e:
		task_dict["status"]["statu"] = "fail"
		try:
			redis_client.set(task_id, json.dumps(task_dict, ensure_ascii=False))
		except Exception as e:
			return {
				"code": 500,
				"status": f"task 4 Separation error status update failed reason is {e}",
			}
		return {
			"code": 500,
			"status": f"task 4 Separation error status update failed reason is {e}",
		}

	print("正在执行克隆任务")
	try:
		# 1、校验音频时长 -- 过长则进行切割
		audio_clip(task_id)
		# 2、获取嗓音ID
		voice_id = CloneVideo(task_id)
		if not voice_id:
			return "voice id get failed"
		await create_el_run(task_id, voice_id)
	except Exception as e:
		task_dict["status"]["statu"] = "fail"
		try:
			redis_client.set(task_id, json.dumps(task_dict, ensure_ascii=False))
		except Exception as e:
			return {
				"code": 500,
				"status": f"task 4 Separation error status update failed reason is {e}",
			}
		return {
			"code": 500,
			"status": f"task 4 Separation error failed reason is {e}",
		}
	print("克隆完毕 --- 执行音频合并")

	try:
		# 获取背景音乐
		audioSeparation(task_id, "2track_acc")

		# 加载人声和背景音乐
		voice = AudioFileClip(f"./allData/output/{task_id}/el.wav")
		background = AudioFileClip(f"./allData/output/{task_id}/separation-1.wav")

		# 获取背景音乐的长度
		background_duration = background.duration
		# 调整人声长度与背景音乐一致
		if voice.duration > background_duration:
			# 如果人声比背景音乐长，裁剪人声
			voice = voice.subclipped(0, background_duration)

		# 合并人声和背景音乐
		mixed = CompositeAudioClip([voice, background])

		# 导出合并后的音频
		mixed.write_audiofile(f"./allData/output/{task_id}/el-1.wav", fps=44100)
		from app import app
		task_dict["status"]["code"] = "4"
		task_dict["status"]["statu"] = "success"
		task_dict["audio"] = f"http://{app.config['IP']}/ai/get/audio?taskId={task_id}"
		try:
			redis_client.set(task_id, json.dumps(task_dict, ensure_ascii=False))
			return f"http://{app.config['IP']}/ai/get/audio?taskId={task_id}"
		except Exception as e:
			return {
				"code": 500,
				"status": f"task 4 status update failed reason is {e}",
			}
	except Exception as e:
		task_dict["status"]["statu"] = "fail"
		try:
			redis_client.set(task_id, json.dumps(task_dict, ensure_ascii=False))
		except Exception as e:
			return {
				"code": 500,
				"status": f"task 4 merge error status update failed reason is {e}",
			}
		return {
			"code": 500,
			"status": f"task 4 merge error failed reason is {e}",
		}

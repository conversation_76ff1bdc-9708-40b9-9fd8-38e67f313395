# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .workspace_group_response_model import WorkspaceGroupResponseModel


class DefaultSharingPreferencesResponseModel(UncheckedBaseModel):
    default_sharing_groups: typing.List[WorkspaceGroupResponseModel] = pydantic.Field()
    """
    List of groups that the user shares with by default
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow

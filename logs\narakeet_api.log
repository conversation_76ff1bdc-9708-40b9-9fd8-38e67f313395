DEBUG:2025-08-06 12:22:36:Using SelectPoller
DEBUG:2025-08-06 12:22:36:Created default connection workflow <pika.adapters.utils.connection_workflow.AMQPConnectionWorkflow object at 0x0000017DC5EEA7B0>
DEBUG:2025-08-06 12:22:36:Starting AMQP Connection workflow asynchronously.
DEBUG:2025-08-06 12:22:36:call_later: added timeout <pika.adapters.select_connection._Timeout object at 0x0000017DC635C4F0> with deadline=7024.031 and callback=functools.partial(<bound method AMQPConnectionWorkflow._start_new_cycle_async of <pika.adapters.utils.connection_workflow.AMQPConnectionWorkflow object at 0x0000017DC5EEA7B0>>, first=True); now=7024.031; delay=0
DEBUG:2025-08-06 12:22:36:Beginning a new AMQP connection workflow cycle; attempts remaining after this: 0
DEBUG:2025-08-06 12:22:36:call_later: added timeout <pika.adapters.select_connection._Timeout object at 0x0000017DC5D7A470> with deadline=7024.031 and callback=<bound method AMQPConnectionWorkflow._try_next_config_async of <pika.adapters.utils.connection_workflow.AMQPConnectionWorkflow object at 0x0000017DC5EEA7B0>>; now=7024.031; delay=0
DEBUG:2025-08-06 12:22:36:_try_next_config_async: '127.0.0.1':5672
DEBUG:2025-08-06 12:22:36:add_callback_threadsafe: added callback=<bound method _AddressResolver._dispatch_result of <pika.adapters.utils.selector_ioloop_adapter._AddressResolver object at 0x0000017DC63D5F10>>
DEBUG:2025-08-06 12:22:36:process_timeouts: invoking callback=<bound method _AddressResolver._dispatch_result of <pika.adapters.utils.selector_ioloop_adapter._AddressResolver object at 0x0000017DC63D5F10>>
DEBUG:2025-08-06 12:22:36:Invoking asynchronous getaddrinfo() completion callback; host='127.0.0.1'
DEBUG:2025-08-06 12:22:36:getaddrinfo returned 1 records
DEBUG:2025-08-06 12:22:36:Attempting to connect using address record (<AddressFamily.AF_INET: 2>, <SocketKind.SOCK_STREAM: 1>, 6, '', ('127.0.0.1', 5672))
INFO:2025-08-06 12:22:36:Pika version 1.3.2 connecting to ('127.0.0.1', 5672)
DEBUG:2025-08-06 12:22:36:add_callback_threadsafe: added callback=<bound method _AsyncSocketConnector._start_async of <pika.adapters.utils.io_services_utils._AsyncSocketConnector object at 0x0000017DC6314140>>
DEBUG:2025-08-06 12:22:36:call_later: added timeout <pika.adapters.select_connection._Timeout object at 0x0000017DC4ADB7F0> with deadline=7034.031 and callback=<bound method AMQPConnector._on_tcp_connection_timeout of <pika.adapters.utils.connection_workflow.AMQPConnector object at 0x0000017DC604B980>>; now=7024.031; delay=10.0
DEBUG:2025-08-06 12:22:36:call_later: added timeout <pika.adapters.select_connection._Timeout object at 0x0000017DC62A37C0> with deadline=7039.031 and callback=<bound method AMQPConnector._on_overall_timeout of <pika.adapters.utils.connection_workflow.AMQPConnector object at 0x0000017DC604B980>>; now=7024.031; delay=15.0
DEBUG:2025-08-06 12:22:36:process_timeouts: invoking callback=<bound method _AsyncSocketConnector._start_async of <pika.adapters.utils.io_services_utils._AsyncSocketConnector object at 0x0000017DC6314140>>
DEBUG:2025-08-06 12:22:36:SelectorIOServicesAdapter.set_writer(912, <bound method _AsyncSocketConnector._on_writable of <pika.adapters.utils.io_services_utils._AsyncSocketConnector object at 0x0000017DC6314140>>)
DEBUG:2025-08-06 12:22:36:set_writer(912, _) added handler Wr
DEBUG:2025-08-06 12:22:36:Connection-establishment is in progress for <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>.
INFO:2025-08-06 12:22:36:Socket connected: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:_AsyncSocketConnector._report_completion(None); <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:SelectorIOServicesAdapter.remove_writer(912)
DEBUG:2025-08-06 12:22:36:remove_writer(912) removed handler
DEBUG:2025-08-06 12:22:36:remove_timeout: removing timeout <pika.adapters.select_connection._Timeout object at 0x0000017DC4ADB7F0> with deadline=7034.031 and callback=<bound method AMQPConnector._on_tcp_connection_timeout of <pika.adapters.utils.connection_workflow.AMQPConnector object at 0x0000017DC604B980>>
DEBUG:2025-08-06 12:22:36:TCP connection to broker established: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>.
DEBUG:2025-08-06 12:22:36:_AsyncStreamConnector.start(); <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:add_callback_threadsafe: added callback=<bound method _AsyncStreamConnector._start_async of <pika.adapters.utils.io_services_utils._AsyncStreamConnector object at 0x0000017DC5EEB740>>
DEBUG:2025-08-06 12:22:36:process_timeouts: invoking callback=<bound method _AsyncStreamConnector._start_async of <pika.adapters.utils.io_services_utils._AsyncStreamConnector object at 0x0000017DC5EEB740>>
DEBUG:2025-08-06 12:22:36:_AsyncStreamConnector._start_async(); <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:_AsyncStreamConnector._linkup()
DEBUG:2025-08-06 12:22:36:New Connection state: CLOSED (prev=CLOSED)
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method Connection._on_connection_start of <SelectConnection CLOSED transport=None params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 1}
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method Connection._on_connection_close_from_broker of <SelectConnection CLOSED transport=None params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 1}
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method Connection._default_on_connection_error of <SelectConnection CLOSED transport=None params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-08-06 12:22:36:New Connection state: INIT (prev=CLOSED)
DEBUG:2025-08-06 12:22:36:Using external connection workflow.
DEBUG:2025-08-06 12:22:36:_AsyncTransportBase.__init__: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:SelectorIOServicesAdapter.set_reader(912, <bound method _AsyncPlaintextTransport._on_socket_readable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540>>)
DEBUG:2025-08-06 12:22:36:set_reader(912, _) added handler Rd
DEBUG:2025-08-06 12:22:36:_linkup(): created transport <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540>
DEBUG:2025-08-06 12:22:36:New Connection state: PROTOCOL (prev=INIT)
DEBUG:2025-08-06 12:22:36:SelectorIOServicesAdapter.set_writer(912, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540>>)
DEBUG:2025-08-06 12:22:36:set_writer(912, _) updated handler RdWr
DEBUG:2025-08-06 12:22:36:Turned on writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:_linkup(): introduced transport to protocol <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540>; _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
DEBUG:2025-08-06 12:22:36:_AsyncStreamConnector._report_completion((<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>)); <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
INFO:2025-08-06 12:22:36:Streaming transport linked up: (<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>).
DEBUG:2025-08-06 12:22:36:Removing callback #0: {'callback': <bound method Connection._default_on_connection_error of <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method AMQPConnector._on_amqp_handshake_done of <pika.adapters.utils.connection_workflow.AMQPConnector object at 0x0000017DC604B980>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method AMQPConnector._on_amqp_handshake_done of <pika.adapters.utils.connection_workflow.AMQPConnector object at 0x0000017DC604B980>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-08-06 12:22:36:_AsyncStreamConnector._cleanup(False)
DEBUG:2025-08-06 12:22:36:SelectorIOServicesAdapter.remove_writer(912)
DEBUG:2025-08-06 12:22:36:remove_writer(912) updated handler Rd
DEBUG:2025-08-06 12:22:36:Turned off writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:Processing 0:Connection.Start
DEBUG:2025-08-06 12:22:36:Processing use of oneshot callback
DEBUG:2025-08-06 12:22:36:0 registered uses left
DEBUG:2025-08-06 12:22:36:Removing callback #0: {'callback': <bound method Connection._on_connection_start of <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 0}
DEBUG:2025-08-06 12:22:36:Calling <bound method Connection._on_connection_start of <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>> for "0:Connection.Start"
DEBUG:2025-08-06 12:22:36:New Connection state: START (prev=PROTOCOL)
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method Connection._on_connection_tune of <SelectConnection START transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 1}
DEBUG:2025-08-06 12:22:36:SelectorIOServicesAdapter.set_writer(912, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540>>)
DEBUG:2025-08-06 12:22:36:set_writer(912, _) updated handler RdWr
DEBUG:2025-08-06 12:22:36:Turned on writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:Recv would block on <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:SelectorIOServicesAdapter.remove_writer(912)
DEBUG:2025-08-06 12:22:36:remove_writer(912) updated handler Rd
DEBUG:2025-08-06 12:22:36:Turned off writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:Processing 0:Connection.Tune
DEBUG:2025-08-06 12:22:36:Processing use of oneshot callback
DEBUG:2025-08-06 12:22:36:0 registered uses left
DEBUG:2025-08-06 12:22:36:Removing callback #0: {'callback': <bound method Connection._on_connection_tune of <SelectConnection START transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 0}
DEBUG:2025-08-06 12:22:36:Calling <bound method Connection._on_connection_tune of <SelectConnection START transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>> for "0:Connection.Tune"
DEBUG:2025-08-06 12:22:36:New Connection state: TUNE (prev=START)
DEBUG:2025-08-06 12:22:36:Creating a HeartbeatChecker: 60
DEBUG:2025-08-06 12:22:36:timeout: 60.000000 send_interval: 30.000000 check_interval: 65.000000
DEBUG:2025-08-06 12:22:36:call_later: added timeout <pika.adapters.select_connection._Timeout object at 0x0000017DC6404DF0> with deadline=7054.031 and callback=<bound method HeartbeatChecker._send_heartbeat of <pika.heartbeat.HeartbeatChecker object at 0x0000017DC63D7F50>>; now=7024.031; delay=30.0
DEBUG:2025-08-06 12:22:36:call_later: added timeout <pika.adapters.select_connection._Timeout object at 0x0000017DC64079A0> with deadline=7089.031 and callback=<bound method HeartbeatChecker._check_heartbeat of <pika.heartbeat.HeartbeatChecker object at 0x0000017DC63D7F50>>; now=7024.031; delay=65
DEBUG:2025-08-06 12:22:36:SelectorIOServicesAdapter.set_writer(912, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540>>)
DEBUG:2025-08-06 12:22:36:set_writer(912, _) updated handler RdWr
DEBUG:2025-08-06 12:22:36:Turned on writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method Connection._on_connection_open_ok of <SelectConnection TUNE transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 1}
DEBUG:2025-08-06 12:22:36:Recv would block on <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:SelectorIOServicesAdapter.remove_writer(912)
DEBUG:2025-08-06 12:22:36:remove_writer(912) updated handler Rd
DEBUG:2025-08-06 12:22:36:Turned off writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:Processing 0:Connection.OpenOk
DEBUG:2025-08-06 12:22:36:Processing use of oneshot callback
DEBUG:2025-08-06 12:22:36:0 registered uses left
DEBUG:2025-08-06 12:22:36:Removing callback #0: {'callback': <bound method Connection._on_connection_open_ok of <SelectConnection TUNE transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 0}
DEBUG:2025-08-06 12:22:36:Calling <bound method Connection._on_connection_open_ok of <SelectConnection TUNE transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>> for "0:Connection.OpenOk"
DEBUG:2025-08-06 12:22:36:New Connection state: OPEN (prev=TUNE)
DEBUG:2025-08-06 12:22:36:Processing 0:_on_connection_open_ok
DEBUG:2025-08-06 12:22:36:Calling <bound method AMQPConnector._on_amqp_handshake_done of <pika.adapters.utils.connection_workflow.AMQPConnector object at 0x0000017DC604B980>> for "0:_on_connection_open_ok"
DEBUG:2025-08-06 12:22:36:AMQPConnector: AMQP handshake attempt completed; state=3; error=None; '127.0.0.1'/(<AddressFamily.AF_INET: 2>, <SocketKind.SOCK_STREAM: 1>, 6, '', ('127.0.0.1', 5672))
DEBUG:2025-08-06 12:22:36:AMQPConnector: AMQP connection established for '127.0.0.1'/(<AddressFamily.AF_INET: 2>, <SocketKind.SOCK_STREAM: 1>, 6, '', ('127.0.0.1', 5672)): <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
INFO:2025-08-06 12:22:36:AMQPConnector - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
DEBUG:2025-08-06 12:22:36:remove_timeout: removing timeout <pika.adapters.select_connection._Timeout object at 0x0000017DC62A37C0> with deadline=7039.031 and callback=<bound method AMQPConnector._on_overall_timeout of <pika.adapters.utils.connection_workflow.AMQPConnector object at 0x0000017DC604B980>>
DEBUG:2025-08-06 12:22:36:Connection attempt completed with <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
INFO:2025-08-06 12:22:36:AMQPConnectionWorkflow - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
DEBUG:2025-08-06 12:22:36:Recv would block on <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
INFO:2025-08-06 12:22:36:Connection workflow succeeded: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method _CallbackResult.set_value_once of <pika.adapters.blocking_connection._CallbackResult object at 0x0000017DC62859C0>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-08-06 12:22:36:Creating channel 1
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method Connection._on_channel_cleanup of <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': True, 'only': <Channel number=1 CLOSED conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'arguments': None, 'calls': 1}
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method Channel._on_getempty of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method Channel._on_cancel of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method Channel._on_flow of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method Channel._on_close_from_broker of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 1}
DEBUG:2025-08-06 12:22:36:SelectorIOServicesAdapter.set_writer(912, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540>>)
DEBUG:2025-08-06 12:22:36:set_writer(912, _) updated handler RdWr
DEBUG:2025-08-06 12:22:36:Turned on writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:Entering blocking state on frame <Channel.Open(['out_of_band='])>; acceptable_replies=[<class 'pika.spec.Channel.OpenOk'>]
DEBUG:2025-08-06 12:22:36:Adding on_synchronous_complete callback
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method Channel._on_synchronous_complete of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 1}
DEBUG:2025-08-06 12:22:36:Adding passed-in RPC response callback
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method Channel._on_openok of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 1}
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method BlockingChannel._on_consumer_cancelled_by_broker of <BlockingChannel impl=<Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method _CallbackResult.signal_once of <pika.adapters.blocking_connection._CallbackResult object at 0x0000017DC642EB80>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method BlockingChannel._on_channel_closed of <BlockingChannel impl=<Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>>, 'one_shot': False, 'only': <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'arguments': None}
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method _CallbackResult.set_value_once of <pika.adapters.blocking_connection._CallbackResult object at 0x0000017DC642E3C0>>, 'one_shot': False, 'only': None, 'arguments': None}
INFO:2025-08-06 12:22:36:Created channel=1
DEBUG:2025-08-06 12:22:36:SelectorIOServicesAdapter.remove_writer(912)
DEBUG:2025-08-06 12:22:36:remove_writer(912) updated handler Rd
DEBUG:2025-08-06 12:22:36:Turned off writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:Processing 1:Channel.OpenOk
DEBUG:2025-08-06 12:22:36:Processing use of oneshot callback
DEBUG:2025-08-06 12:22:36:0 registered uses left
DEBUG:2025-08-06 12:22:36:Removing callback #0: {'callback': <bound method Channel._on_synchronous_complete of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 0}
DEBUG:2025-08-06 12:22:36:Processing use of oneshot callback
DEBUG:2025-08-06 12:22:36:0 registered uses left
DEBUG:2025-08-06 12:22:36:Removing callback #0: {'callback': <bound method Channel._on_openok of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 0}
DEBUG:2025-08-06 12:22:36:Calling <bound method Channel._on_synchronous_complete of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>> for "1:Channel.OpenOk"
DEBUG:2025-08-06 12:22:36:0 blocked frames
DEBUG:2025-08-06 12:22:36:Calling <bound method Channel._on_openok of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>> for "1:Channel.OpenOk"
DEBUG:2025-08-06 12:22:36:Recv would block on <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:SelectorIOServicesAdapter.set_writer(912, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540>>)
DEBUG:2025-08-06 12:22:36:set_writer(912, _) updated handler RdWr
DEBUG:2025-08-06 12:22:36:Turned on writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:Entering blocking state on frame <Queue.Declare(['arguments={}', 'auto_delete=False', 'durable=True', 'exclusive=False', 'nowait=False', 'passive=False', 'queue=ai_dispose', 'ticket=0'])>; acceptable_replies=[(<class 'pika.spec.Queue.DeclareOk'>, {'queue': 'ai_dispose'})]
DEBUG:2025-08-06 12:22:36:Adding on_synchronous_complete callback
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method Channel._on_synchronous_complete of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': {'queue': 'ai_dispose'}, 'calls': 1}
DEBUG:2025-08-06 12:22:36:Adding passed-in RPC response callback
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method _CallbackResult.set_value_once of <pika.adapters.blocking_connection._CallbackResult object at 0x0000017DC6422980>>, 'one_shot': True, 'only': None, 'arguments': {'queue': 'ai_dispose'}, 'calls': 1}
DEBUG:2025-08-06 12:22:36:SelectorIOServicesAdapter.remove_writer(912)
DEBUG:2025-08-06 12:22:36:remove_writer(912) updated handler Rd
DEBUG:2025-08-06 12:22:36:Turned off writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:Processing 1:Queue.DeclareOk
DEBUG:2025-08-06 12:22:36:Processing use of oneshot callback
DEBUG:2025-08-06 12:22:36:0 registered uses left
DEBUG:2025-08-06 12:22:36:Comparing {'queue': 'ai_dispose'} to {'queue': 'ai_dispose'}
DEBUG:2025-08-06 12:22:36:Removing callback #0: {'callback': <bound method Channel._on_synchronous_complete of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': {'queue': 'ai_dispose'}, 'calls': 0}
DEBUG:2025-08-06 12:22:36:Processing use of oneshot callback
DEBUG:2025-08-06 12:22:36:0 registered uses left
DEBUG:2025-08-06 12:22:36:Comparing {'queue': 'ai_dispose'} to {'queue': 'ai_dispose'}
DEBUG:2025-08-06 12:22:36:Removing callback #0: {'callback': <bound method _CallbackResult.set_value_once of <pika.adapters.blocking_connection._CallbackResult object at 0x0000017DC6422980>>, 'one_shot': True, 'only': None, 'arguments': {'queue': 'ai_dispose'}, 'calls': 0}
DEBUG:2025-08-06 12:22:36:Calling <bound method Channel._on_synchronous_complete of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>> for "1:Queue.DeclareOk"
DEBUG:2025-08-06 12:22:36:0 blocked frames
DEBUG:2025-08-06 12:22:36:Calling <bound method _CallbackResult.set_value_once of <pika.adapters.blocking_connection._CallbackResult object at 0x0000017DC6422980>> for "1:Queue.DeclareOk"
DEBUG:2025-08-06 12:22:36:Recv would block on <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:SelectorIOServicesAdapter.set_writer(912, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540>>)
DEBUG:2025-08-06 12:22:36:set_writer(912, _) updated handler RdWr
DEBUG:2025-08-06 12:22:36:Turned on writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:Entering blocking state on frame <Basic.Qos(['global_qos=False', 'prefetch_count=1', 'prefetch_size=0'])>; acceptable_replies=[<class 'pika.spec.Basic.QosOk'>]
DEBUG:2025-08-06 12:22:36:Adding on_synchronous_complete callback
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method Channel._on_synchronous_complete of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 1}
DEBUG:2025-08-06 12:22:36:Adding passed-in RPC response callback
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method _CallbackResult.signal_once of <pika.adapters.blocking_connection._CallbackResult object at 0x0000017DC63CD380>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 1}
DEBUG:2025-08-06 12:22:36:SelectorIOServicesAdapter.remove_writer(912)
DEBUG:2025-08-06 12:22:36:remove_writer(912) updated handler Rd
DEBUG:2025-08-06 12:22:36:Turned off writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:Processing 1:Basic.QosOk
DEBUG:2025-08-06 12:22:36:Processing use of oneshot callback
DEBUG:2025-08-06 12:22:36:0 registered uses left
DEBUG:2025-08-06 12:22:36:Removing callback #0: {'callback': <bound method Channel._on_synchronous_complete of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 0}
DEBUG:2025-08-06 12:22:36:Processing use of oneshot callback
DEBUG:2025-08-06 12:22:36:0 registered uses left
DEBUG:2025-08-06 12:22:36:Removing callback #0: {'callback': <bound method _CallbackResult.signal_once of <pika.adapters.blocking_connection._CallbackResult object at 0x0000017DC63CD380>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 0}
DEBUG:2025-08-06 12:22:36:Calling <bound method Channel._on_synchronous_complete of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>> for "1:Basic.QosOk"
DEBUG:2025-08-06 12:22:36:0 blocked frames
DEBUG:2025-08-06 12:22:36:Calling <bound method _CallbackResult.signal_once of <pika.adapters.blocking_connection._CallbackResult object at 0x0000017DC63CD380>> for "1:Basic.QosOk"
DEBUG:2025-08-06 12:22:36:Recv would block on <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:SelectorIOServicesAdapter.set_writer(912, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540>>)
DEBUG:2025-08-06 12:22:36:set_writer(912, _) updated handler RdWr
DEBUG:2025-08-06 12:22:36:Turned on writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:Entering blocking state on frame <Basic.Consume(['arguments={}', 'consumer_tag=ctag1.cb1d004cd348492dbb514c58650f740d', 'exclusive=False', 'no_ack=False', 'no_local=False', 'nowait=False', 'queue=ai_dispose', 'ticket=0'])>; acceptable_replies=[(<class 'pika.spec.Basic.ConsumeOk'>, {'consumer_tag': 'ctag1.cb1d004cd348492dbb514c58650f740d'})]
DEBUG:2025-08-06 12:22:36:Adding on_synchronous_complete callback
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method Channel._on_synchronous_complete of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': {'consumer_tag': 'ctag1.cb1d004cd348492dbb514c58650f740d'}, 'calls': 1}
DEBUG:2025-08-06 12:22:36:Adding passed-in RPC response callback
DEBUG:2025-08-06 12:22:36:Added: {'callback': <bound method Channel._on_eventok of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': {'consumer_tag': 'ctag1.cb1d004cd348492dbb514c58650f740d'}, 'calls': 1}
DEBUG:2025-08-06 12:22:36:SelectorIOServicesAdapter.remove_writer(912)
DEBUG:2025-08-06 12:22:36:remove_writer(912) updated handler Rd
DEBUG:2025-08-06 12:22:36:Turned off writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:36:Processing 1:Basic.ConsumeOk
DEBUG:2025-08-06 12:22:36:Processing use of oneshot callback
DEBUG:2025-08-06 12:22:36:0 registered uses left
DEBUG:2025-08-06 12:22:36:Comparing {'consumer_tag': 'ctag1.cb1d004cd348492dbb514c58650f740d'} to {'consumer_tag': 'ctag1.cb1d004cd348492dbb514c58650f740d'}
DEBUG:2025-08-06 12:22:36:Removing callback #1: {'callback': <bound method Channel._on_synchronous_complete of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': {'consumer_tag': 'ctag1.cb1d004cd348492dbb514c58650f740d'}, 'calls': 0}
DEBUG:2025-08-06 12:22:36:Processing use of oneshot callback
DEBUG:2025-08-06 12:22:36:0 registered uses left
DEBUG:2025-08-06 12:22:36:Comparing {'consumer_tag': 'ctag1.cb1d004cd348492dbb514c58650f740d'} to {'consumer_tag': 'ctag1.cb1d004cd348492dbb514c58650f740d'}
DEBUG:2025-08-06 12:22:36:Removing callback #1: {'callback': <bound method Channel._on_eventok of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': {'consumer_tag': 'ctag1.cb1d004cd348492dbb514c58650f740d'}, 'calls': 0}
DEBUG:2025-08-06 12:22:36:Calling <bound method _CallbackResult.signal_once of <pika.adapters.blocking_connection._CallbackResult object at 0x0000017DC642EB80>> for "1:Basic.ConsumeOk"
DEBUG:2025-08-06 12:22:36:Calling <bound method Channel._on_synchronous_complete of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>> for "1:Basic.ConsumeOk"
DEBUG:2025-08-06 12:22:36:0 blocked frames
DEBUG:2025-08-06 12:22:36:Calling <bound method Channel._on_eventok of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>> for "1:Basic.ConsumeOk"
DEBUG:2025-08-06 12:22:36:Discarding frame <METHOD(['channel_number=1', 'frame_type=1', "method=<Basic.ConsumeOk(['consumer_tag=ctag1.cb1d004cd348492dbb514c58650f740d'])>"])>
DEBUG:2025-08-06 12:22:36:Recv would block on <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:41:Recv would block on <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:41:SelectorIOServicesAdapter.set_writer(912, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540>>)
DEBUG:2025-08-06 12:22:41:set_writer(912, _) updated handler RdWr
DEBUG:2025-08-06 12:22:41:Turned on writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:22:41:SelectorIOServicesAdapter.remove_writer(912)
DEBUG:2025-08-06 12:22:41:remove_writer(912) updated handler Rd
DEBUG:2025-08-06 12:22:41:Turned off writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:23:06:Received heartbeat frame
DEBUG:2025-08-06 12:23:06:Recv would block on <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:23:06:Sending heartbeat frame
DEBUG:2025-08-06 12:23:06:Sending heartbeat frame
DEBUG:2025-08-06 12:23:06:SelectorIOServicesAdapter.set_writer(912, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540>>)
DEBUG:2025-08-06 12:23:06:set_writer(912, _) updated handler RdWr
DEBUG:2025-08-06 12:23:06:Turned on writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:23:06:call_later: added timeout <pika.adapters.select_connection._Timeout object at 0x0000017DC4ADB7F0> with deadline=7084.031 and callback=<bound method HeartbeatChecker._send_heartbeat of <pika.heartbeat.HeartbeatChecker object at 0x0000017DC63D7F50>>; now=7054.031; delay=30.0
DEBUG:2025-08-06 12:23:06:SelectorIOServicesAdapter.remove_writer(912)
DEBUG:2025-08-06 12:23:06:remove_writer(912) updated handler Rd
DEBUG:2025-08-06 12:23:06:Turned off writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:23:36:Received heartbeat frame
DEBUG:2025-08-06 12:23:36:Recv would block on <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:23:36:Sending heartbeat frame
DEBUG:2025-08-06 12:23:36:Sending heartbeat frame
DEBUG:2025-08-06 12:23:36:SelectorIOServicesAdapter.set_writer(912, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540>>)
DEBUG:2025-08-06 12:23:36:set_writer(912, _) updated handler RdWr
DEBUG:2025-08-06 12:23:36:Turned on writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:23:36:call_later: added timeout <pika.adapters.select_connection._Timeout object at 0x0000017DC7D19A80> with deadline=7114.031 and callback=<bound method HeartbeatChecker._send_heartbeat of <pika.heartbeat.HeartbeatChecker object at 0x0000017DC63D7F50>>; now=7084.031; delay=30.0
DEBUG:2025-08-06 12:23:36:SelectorIOServicesAdapter.remove_writer(912)
DEBUG:2025-08-06 12:23:36:remove_writer(912) updated handler Rd
DEBUG:2025-08-06 12:23:36:Turned off writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:23:41:Received 2 heartbeat frames, sent 2, idle intervals 0
DEBUG:2025-08-06 12:23:41:call_later: added timeout <pika.adapters.select_connection._Timeout object at 0x0000017DC7833370> with deadline=7154.031 and callback=<bound method HeartbeatChecker._check_heartbeat of <pika.heartbeat.HeartbeatChecker object at 0x0000017DC63D7F50>>; now=7089.031; delay=65
DEBUG:2025-08-06 12:24:01:Recv would block on <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:24:01:SelectorIOServicesAdapter.set_writer(912, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540>>)
DEBUG:2025-08-06 12:24:01:set_writer(912, _) updated handler RdWr
DEBUG:2025-08-06 12:24:01:Turned on writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:24:01:SelectorIOServicesAdapter.remove_writer(912)
DEBUG:2025-08-06 12:24:01:remove_writer(912) updated handler Rd
DEBUG:2025-08-06 12:24:01:Turned off writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:24:05:Recv would block on <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:24:05:SelectorIOServicesAdapter.set_writer(912, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540>>)
DEBUG:2025-08-06 12:24:05:set_writer(912, _) updated handler RdWr
DEBUG:2025-08-06 12:24:05:Turned on writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:24:05:SelectorIOServicesAdapter.remove_writer(912)
DEBUG:2025-08-06 12:24:05:remove_writer(912) updated handler Rd
DEBUG:2025-08-06 12:24:05:Turned off writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:24:06:Sending heartbeat frame
DEBUG:2025-08-06 12:24:06:Sending heartbeat frame
DEBUG:2025-08-06 12:24:06:SelectorIOServicesAdapter.set_writer(912, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017DC62D2540>>)
DEBUG:2025-08-06 12:24:06:set_writer(912, _) updated handler RdWr
DEBUG:2025-08-06 12:24:06:Turned on writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>
DEBUG:2025-08-06 12:24:06:call_later: added timeout <pika.adapters.select_connection._Timeout object at 0x0000017DC7D92770> with deadline=7144.046 and callback=<bound method HeartbeatChecker._send_heartbeat of <pika.heartbeat.HeartbeatChecker object at 0x0000017DC63D7F50>>; now=7114.046; delay=30.0
DEBUG:2025-08-06 12:24:06:SelectorIOServicesAdapter.remove_writer(912)
DEBUG:2025-08-06 12:24:06:remove_writer(912) updated handler Rd
DEBUG:2025-08-06 12:24:06:Turned off writability watcher: <socket.socket fd=912, family=2, type=1, proto=6, laddr=('127.0.0.1', 65192), raddr=('127.0.0.1', 5672)>

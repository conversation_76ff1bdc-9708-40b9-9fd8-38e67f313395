DEBUG:2025-26-06 10:43:50:load_ssl_context verify=True cert=None trust_env=True http2=False
DEBUG:2025-26-06 10:43:50:load_verify_locations cafile='F:\\dev\\anaconda3\\Library\\ssl\\cacert.pem'
DEBUG:2025-26-06 10:43:50:load_ssl_context verify=True cert=None trust_env=True http2=False
DEBUG:2025-26-06 10:43:50:load_verify_locations cafile='F:\\dev\\anaconda3\\Library\\ssl\\cacert.pem'
DEBUG:2025-26-06 10:43:51:load_ssl_context verify=True cert=None trust_env=True http2=False
DEBUG:2025-26-06 10:43:51:load_verify_locations cafile='F:\\dev\\anaconda3\\Library\\ssl\\cacert.pem'
DEBUG:2025-26-06 10:43:51:Using SelectPoller
DEBUG:2025-26-06 10:43:51:Created default connection workflow <pika.adapters.utils.connection_workflow.AMQPConnectionWorkflow object at 0x000001BD6F7F5160>
DEBUG:2025-26-06 10:43:51:Starting AMQP Connection workflow asynchronously.
DEBUG:2025-26-06 10:43:51:call_later: added timeout <pika.adapters.select_connection._Timeout object at 0x000001BD6F792AD0> with deadline=53964.5 and callback=functools.partial(<bound method AMQPConnectionWorkflow._start_new_cycle_async of <pika.adapters.utils.connection_workflow.AMQPConnectionWorkflow object at 0x000001BD6F7F5160>>, first=True); now=53964.5; delay=0
DEBUG:2025-26-06 10:43:51:Beginning a new AMQP connection workflow cycle; attempts remaining after this: 0
DEBUG:2025-26-06 10:43:51:call_later: added timeout <pika.adapters.select_connection._Timeout object at 0x000001BD6F7929E0> with deadline=53964.5 and callback=<bound method AMQPConnectionWorkflow._try_next_config_async of <pika.adapters.utils.connection_workflow.AMQPConnectionWorkflow object at 0x000001BD6F7F5160>>; now=53964.5; delay=0
DEBUG:2025-26-06 10:43:51:_try_next_config_async: '127.0.0.1':5672
DEBUG:2025-26-06 10:43:51:add_callback_threadsafe: added callback=<bound method _AddressResolver._dispatch_result of <pika.adapters.utils.selector_ioloop_adapter._AddressResolver object at 0x000001BD6F582AB0>>
DEBUG:2025-26-06 10:43:51:process_timeouts: invoking callback=<bound method _AddressResolver._dispatch_result of <pika.adapters.utils.selector_ioloop_adapter._AddressResolver object at 0x000001BD6F582AB0>>
DEBUG:2025-26-06 10:43:51:Invoking asynchronous getaddrinfo() completion callback; host='127.0.0.1'
DEBUG:2025-26-06 10:43:51:getaddrinfo returned 1 records
DEBUG:2025-26-06 10:43:51:Attempting to connect using address record (<AddressFamily.AF_INET: 2>, <SocketKind.SOCK_STREAM: 1>, 6, '', ('127.0.0.1', 5672))
INFO:2025-26-06 10:43:51:Pika version 1.3.2 connecting to ('127.0.0.1', 5672)
DEBUG:2025-26-06 10:43:51:add_callback_threadsafe: added callback=<bound method _AsyncSocketConnector._start_async of <pika.adapters.utils.io_services_utils._AsyncSocketConnector object at 0x000001BD6F7B6F60>>
DEBUG:2025-26-06 10:43:51:call_later: added timeout <pika.adapters.select_connection._Timeout object at 0x000001BD6F3FB1C0> with deadline=53974.5 and callback=<bound method AMQPConnector._on_tcp_connection_timeout of <pika.adapters.utils.connection_workflow.AMQPConnector object at 0x000001BD6F6BEF00>>; now=53964.5; delay=10.0
DEBUG:2025-26-06 10:43:51:call_later: added timeout <pika.adapters.select_connection._Timeout object at 0x000001BD6F792AD0> with deadline=53979.5 and callback=<bound method AMQPConnector._on_overall_timeout of <pika.adapters.utils.connection_workflow.AMQPConnector object at 0x000001BD6F6BEF00>>; now=53964.5; delay=15.0
DEBUG:2025-26-06 10:43:51:process_timeouts: invoking callback=<bound method _AsyncSocketConnector._start_async of <pika.adapters.utils.io_services_utils._AsyncSocketConnector object at 0x000001BD6F7B6F60>>
DEBUG:2025-26-06 10:43:51:SelectorIOServicesAdapter.set_writer(1424, <bound method _AsyncSocketConnector._on_writable of <pika.adapters.utils.io_services_utils._AsyncSocketConnector object at 0x000001BD6F7B6F60>>)
DEBUG:2025-26-06 10:43:51:set_writer(1424, _) added handler Wr
DEBUG:2025-26-06 10:43:51:Connection-establishment is in progress for <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>.
INFO:2025-26-06 10:43:51:Socket connected: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:_AsyncSocketConnector._report_completion(None); <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:SelectorIOServicesAdapter.remove_writer(1424)
DEBUG:2025-26-06 10:43:51:remove_writer(1424) removed handler
DEBUG:2025-26-06 10:43:51:remove_timeout: removing timeout <pika.adapters.select_connection._Timeout object at 0x000001BD6F3FB1C0> with deadline=53974.5 and callback=<bound method AMQPConnector._on_tcp_connection_timeout of <pika.adapters.utils.connection_workflow.AMQPConnector object at 0x000001BD6F6BEF00>>
DEBUG:2025-26-06 10:43:51:TCP connection to broker established: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>.
DEBUG:2025-26-06 10:43:51:_AsyncStreamConnector.start(); <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:add_callback_threadsafe: added callback=<bound method _AsyncStreamConnector._start_async of <pika.adapters.utils.io_services_utils._AsyncStreamConnector object at 0x000001BD6F7929F0>>
DEBUG:2025-26-06 10:43:51:process_timeouts: invoking callback=<bound method _AsyncStreamConnector._start_async of <pika.adapters.utils.io_services_utils._AsyncStreamConnector object at 0x000001BD6F7929F0>>
DEBUG:2025-26-06 10:43:51:_AsyncStreamConnector._start_async(); <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:_AsyncStreamConnector._linkup()
DEBUG:2025-26-06 10:43:51:New Connection state: CLOSED (prev=CLOSED)
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method Connection._on_connection_start of <SelectConnection CLOSED transport=None params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 1}
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method Connection._on_connection_close_from_broker of <SelectConnection CLOSED transport=None params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 1}
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method Connection._default_on_connection_error of <SelectConnection CLOSED transport=None params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-26-06 10:43:51:New Connection state: INIT (prev=CLOSED)
DEBUG:2025-26-06 10:43:51:Using external connection workflow.
DEBUG:2025-26-06 10:43:51:_AsyncTransportBase.__init__: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:SelectorIOServicesAdapter.set_reader(1424, <bound method _AsyncPlaintextTransport._on_socket_readable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470>>)
DEBUG:2025-26-06 10:43:51:set_reader(1424, _) added handler Rd
DEBUG:2025-26-06 10:43:51:_linkup(): created transport <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470>
DEBUG:2025-26-06 10:43:51:New Connection state: PROTOCOL (prev=INIT)
DEBUG:2025-26-06 10:43:51:SelectorIOServicesAdapter.set_writer(1424, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470>>)
DEBUG:2025-26-06 10:43:51:set_writer(1424, _) updated handler RdWr
DEBUG:2025-26-06 10:43:51:Turned on writability watcher: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:_linkup(): introduced transport to protocol <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470>; _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
DEBUG:2025-26-06 10:43:51:_AsyncStreamConnector._report_completion((<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>)); <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
INFO:2025-26-06 10:43:51:Streaming transport linked up: (<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>).
DEBUG:2025-26-06 10:43:51:Removing callback #0: {'callback': <bound method Connection._default_on_connection_error of <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method AMQPConnector._on_amqp_handshake_done of <pika.adapters.utils.connection_workflow.AMQPConnector object at 0x000001BD6F6BEF00>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method AMQPConnector._on_amqp_handshake_done of <pika.adapters.utils.connection_workflow.AMQPConnector object at 0x000001BD6F6BEF00>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-26-06 10:43:51:_AsyncStreamConnector._cleanup(False)
DEBUG:2025-26-06 10:43:51:SelectorIOServicesAdapter.remove_writer(1424)
DEBUG:2025-26-06 10:43:51:remove_writer(1424) updated handler Rd
DEBUG:2025-26-06 10:43:51:Turned off writability watcher: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:Processing 0:Connection.Start
DEBUG:2025-26-06 10:43:51:Processing use of oneshot callback
DEBUG:2025-26-06 10:43:51:0 registered uses left
DEBUG:2025-26-06 10:43:51:Removing callback #0: {'callback': <bound method Connection._on_connection_start of <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 0}
DEBUG:2025-26-06 10:43:51:Calling <bound method Connection._on_connection_start of <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>> for "0:Connection.Start"
DEBUG:2025-26-06 10:43:51:New Connection state: START (prev=PROTOCOL)
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method Connection._on_connection_tune of <SelectConnection START transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 1}
DEBUG:2025-26-06 10:43:51:SelectorIOServicesAdapter.set_writer(1424, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470>>)
DEBUG:2025-26-06 10:43:51:set_writer(1424, _) updated handler RdWr
DEBUG:2025-26-06 10:43:51:Turned on writability watcher: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:Recv would block on <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:SelectorIOServicesAdapter.remove_writer(1424)
DEBUG:2025-26-06 10:43:51:remove_writer(1424) updated handler Rd
DEBUG:2025-26-06 10:43:51:Turned off writability watcher: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:Processing 0:Connection.Tune
DEBUG:2025-26-06 10:43:51:Processing use of oneshot callback
DEBUG:2025-26-06 10:43:51:0 registered uses left
DEBUG:2025-26-06 10:43:51:Removing callback #0: {'callback': <bound method Connection._on_connection_tune of <SelectConnection START transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 0}
DEBUG:2025-26-06 10:43:51:Calling <bound method Connection._on_connection_tune of <SelectConnection START transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>> for "0:Connection.Tune"
DEBUG:2025-26-06 10:43:51:New Connection state: TUNE (prev=START)
DEBUG:2025-26-06 10:43:51:Creating a HeartbeatChecker: 60
DEBUG:2025-26-06 10:43:51:timeout: 60.000000 send_interval: 30.000000 check_interval: 65.000000
DEBUG:2025-26-06 10:43:51:call_later: added timeout <pika.adapters.select_connection._Timeout object at 0x000001BD6F7F52A0> with deadline=53994.5 and callback=<bound method HeartbeatChecker._send_heartbeat of <pika.heartbeat.HeartbeatChecker object at 0x000001BD6F7F5280>>; now=53964.5; delay=30.0
DEBUG:2025-26-06 10:43:51:call_later: added timeout <pika.adapters.select_connection._Timeout object at 0x000001BD6F7F5210> with deadline=54029.5 and callback=<bound method HeartbeatChecker._check_heartbeat of <pika.heartbeat.HeartbeatChecker object at 0x000001BD6F7F5280>>; now=53964.5; delay=65
DEBUG:2025-26-06 10:43:51:SelectorIOServicesAdapter.set_writer(1424, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470>>)
DEBUG:2025-26-06 10:43:51:set_writer(1424, _) updated handler RdWr
DEBUG:2025-26-06 10:43:51:Turned on writability watcher: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method Connection._on_connection_open_ok of <SelectConnection TUNE transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 1}
DEBUG:2025-26-06 10:43:51:Recv would block on <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:SelectorIOServicesAdapter.remove_writer(1424)
DEBUG:2025-26-06 10:43:51:remove_writer(1424) updated handler Rd
DEBUG:2025-26-06 10:43:51:Turned off writability watcher: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:Processing 0:Connection.OpenOk
DEBUG:2025-26-06 10:43:51:Processing use of oneshot callback
DEBUG:2025-26-06 10:43:51:0 registered uses left
DEBUG:2025-26-06 10:43:51:Removing callback #0: {'callback': <bound method Connection._on_connection_open_ok of <SelectConnection TUNE transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 0}
DEBUG:2025-26-06 10:43:51:Calling <bound method Connection._on_connection_open_ok of <SelectConnection TUNE transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>> for "0:Connection.OpenOk"
DEBUG:2025-26-06 10:43:51:New Connection state: OPEN (prev=TUNE)
DEBUG:2025-26-06 10:43:51:Processing 0:_on_connection_open_ok
DEBUG:2025-26-06 10:43:51:Calling <bound method AMQPConnector._on_amqp_handshake_done of <pika.adapters.utils.connection_workflow.AMQPConnector object at 0x000001BD6F6BEF00>> for "0:_on_connection_open_ok"
DEBUG:2025-26-06 10:43:51:AMQPConnector: AMQP handshake attempt completed; state=3; error=None; '127.0.0.1'/(<AddressFamily.AF_INET: 2>, <SocketKind.SOCK_STREAM: 1>, 6, '', ('127.0.0.1', 5672))
DEBUG:2025-26-06 10:43:51:AMQPConnector: AMQP connection established for '127.0.0.1'/(<AddressFamily.AF_INET: 2>, <SocketKind.SOCK_STREAM: 1>, 6, '', ('127.0.0.1', 5672)): <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
INFO:2025-26-06 10:43:51:AMQPConnector - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
DEBUG:2025-26-06 10:43:51:remove_timeout: removing timeout <pika.adapters.select_connection._Timeout object at 0x000001BD6F792AD0> with deadline=53979.5 and callback=<bound method AMQPConnector._on_overall_timeout of <pika.adapters.utils.connection_workflow.AMQPConnector object at 0x000001BD6F6BEF00>>
DEBUG:2025-26-06 10:43:51:Connection attempt completed with <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
INFO:2025-26-06 10:43:51:AMQPConnectionWorkflow - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
DEBUG:2025-26-06 10:43:51:Recv would block on <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
INFO:2025-26-06 10:43:51:Connection workflow succeeded: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method _CallbackResult.set_value_once of <pika.adapters.blocking_connection._CallbackResult object at 0x000001BD6F205840>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-26-06 10:43:51:Creating channel 1
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method Connection._on_channel_cleanup of <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'one_shot': True, 'only': <Channel number=1 CLOSED conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'arguments': None, 'calls': 1}
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method Channel._on_getempty of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method Channel._on_cancel of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method Channel._on_flow of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method Channel._on_close_from_broker of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 1}
DEBUG:2025-26-06 10:43:51:SelectorIOServicesAdapter.set_writer(1424, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470>>)
DEBUG:2025-26-06 10:43:51:set_writer(1424, _) updated handler RdWr
DEBUG:2025-26-06 10:43:51:Turned on writability watcher: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:Entering blocking state on frame <Channel.Open(['out_of_band='])>; acceptable_replies=[<class 'pika.spec.Channel.OpenOk'>]
DEBUG:2025-26-06 10:43:51:Adding on_synchronous_complete callback
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method Channel._on_synchronous_complete of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 1}
DEBUG:2025-26-06 10:43:51:Adding passed-in RPC response callback
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method Channel._on_openok of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 1}
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method BlockingChannel._on_consumer_cancelled_by_broker of <BlockingChannel impl=<Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method _CallbackResult.signal_once of <pika.adapters.blocking_connection._CallbackResult object at 0x000001BD6F80D080>>, 'one_shot': False, 'only': None, 'arguments': None}
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method BlockingChannel._on_channel_closed of <BlockingChannel impl=<Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>>, 'one_shot': False, 'only': <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>, 'arguments': None}
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method _CallbackResult.set_value_once of <pika.adapters.blocking_connection._CallbackResult object at 0x000001BD6F80D040>>, 'one_shot': False, 'only': None, 'arguments': None}
INFO:2025-26-06 10:43:51:Created channel=1
DEBUG:2025-26-06 10:43:51:SelectorIOServicesAdapter.remove_writer(1424)
DEBUG:2025-26-06 10:43:51:remove_writer(1424) updated handler Rd
DEBUG:2025-26-06 10:43:51:Turned off writability watcher: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:Processing 1:Channel.OpenOk
DEBUG:2025-26-06 10:43:51:Processing use of oneshot callback
DEBUG:2025-26-06 10:43:51:0 registered uses left
DEBUG:2025-26-06 10:43:51:Removing callback #0: {'callback': <bound method Channel._on_synchronous_complete of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 0}
DEBUG:2025-26-06 10:43:51:Processing use of oneshot callback
DEBUG:2025-26-06 10:43:51:0 registered uses left
DEBUG:2025-26-06 10:43:51:Removing callback #0: {'callback': <bound method Channel._on_openok of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 0}
DEBUG:2025-26-06 10:43:51:Calling <bound method Channel._on_synchronous_complete of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>> for "1:Channel.OpenOk"
DEBUG:2025-26-06 10:43:51:0 blocked frames
DEBUG:2025-26-06 10:43:51:Calling <bound method Channel._on_openok of <Channel number=1 OPENING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>> for "1:Channel.OpenOk"
DEBUG:2025-26-06 10:43:51:Recv would block on <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:SelectorIOServicesAdapter.set_writer(1424, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470>>)
DEBUG:2025-26-06 10:43:51:set_writer(1424, _) updated handler RdWr
DEBUG:2025-26-06 10:43:51:Turned on writability watcher: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:Entering blocking state on frame <Queue.Declare(['arguments={}', 'auto_delete=False', 'durable=True', 'exclusive=False', 'nowait=False', 'passive=False', 'queue=ai_dispose', 'ticket=0'])>; acceptable_replies=[(<class 'pika.spec.Queue.DeclareOk'>, {'queue': 'ai_dispose'})]
DEBUG:2025-26-06 10:43:51:Adding on_synchronous_complete callback
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method Channel._on_synchronous_complete of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': {'queue': 'ai_dispose'}, 'calls': 1}
DEBUG:2025-26-06 10:43:51:Adding passed-in RPC response callback
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method _CallbackResult.set_value_once of <pika.adapters.blocking_connection._CallbackResult object at 0x000001BD6F205680>>, 'one_shot': True, 'only': None, 'arguments': {'queue': 'ai_dispose'}, 'calls': 1}
DEBUG:2025-26-06 10:43:51:SelectorIOServicesAdapter.remove_writer(1424)
DEBUG:2025-26-06 10:43:51:remove_writer(1424) updated handler Rd
DEBUG:2025-26-06 10:43:51:Turned off writability watcher: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:Processing 1:Queue.DeclareOk
DEBUG:2025-26-06 10:43:51:Processing use of oneshot callback
DEBUG:2025-26-06 10:43:51:0 registered uses left
DEBUG:2025-26-06 10:43:51:Comparing {'queue': 'ai_dispose'} to {'queue': 'ai_dispose'}
DEBUG:2025-26-06 10:43:51:Removing callback #0: {'callback': <bound method Channel._on_synchronous_complete of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': {'queue': 'ai_dispose'}, 'calls': 0}
DEBUG:2025-26-06 10:43:51:Processing use of oneshot callback
DEBUG:2025-26-06 10:43:51:0 registered uses left
DEBUG:2025-26-06 10:43:51:Comparing {'queue': 'ai_dispose'} to {'queue': 'ai_dispose'}
DEBUG:2025-26-06 10:43:51:Removing callback #0: {'callback': <bound method _CallbackResult.set_value_once of <pika.adapters.blocking_connection._CallbackResult object at 0x000001BD6F205680>>, 'one_shot': True, 'only': None, 'arguments': {'queue': 'ai_dispose'}, 'calls': 0}
DEBUG:2025-26-06 10:43:51:Calling <bound method Channel._on_synchronous_complete of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>> for "1:Queue.DeclareOk"
DEBUG:2025-26-06 10:43:51:0 blocked frames
DEBUG:2025-26-06 10:43:51:Calling <bound method _CallbackResult.set_value_once of <pika.adapters.blocking_connection._CallbackResult object at 0x000001BD6F205680>> for "1:Queue.DeclareOk"
DEBUG:2025-26-06 10:43:51:Recv would block on <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:SelectorIOServicesAdapter.set_writer(1424, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470>>)
DEBUG:2025-26-06 10:43:51:set_writer(1424, _) updated handler RdWr
DEBUG:2025-26-06 10:43:51:Turned on writability watcher: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:Entering blocking state on frame <Basic.Qos(['global_qos=False', 'prefetch_count=1', 'prefetch_size=0'])>; acceptable_replies=[<class 'pika.spec.Basic.QosOk'>]
DEBUG:2025-26-06 10:43:51:Adding on_synchronous_complete callback
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method Channel._on_synchronous_complete of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 1}
DEBUG:2025-26-06 10:43:51:Adding passed-in RPC response callback
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method _CallbackResult.signal_once of <pika.adapters.blocking_connection._CallbackResult object at 0x000001BD6F193380>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 1}
DEBUG:2025-26-06 10:43:51:SelectorIOServicesAdapter.remove_writer(1424)
DEBUG:2025-26-06 10:43:51:remove_writer(1424) updated handler Rd
DEBUG:2025-26-06 10:43:51:Turned off writability watcher: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:Processing 1:Basic.QosOk
DEBUG:2025-26-06 10:43:51:Processing use of oneshot callback
DEBUG:2025-26-06 10:43:51:0 registered uses left
DEBUG:2025-26-06 10:43:51:Removing callback #0: {'callback': <bound method Channel._on_synchronous_complete of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 0}
DEBUG:2025-26-06 10:43:51:Processing use of oneshot callback
DEBUG:2025-26-06 10:43:51:0 registered uses left
DEBUG:2025-26-06 10:43:51:Removing callback #0: {'callback': <bound method _CallbackResult.signal_once of <pika.adapters.blocking_connection._CallbackResult object at 0x000001BD6F193380>>, 'one_shot': True, 'only': None, 'arguments': None, 'calls': 0}
DEBUG:2025-26-06 10:43:51:Calling <bound method Channel._on_synchronous_complete of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>> for "1:Basic.QosOk"
DEBUG:2025-26-06 10:43:51:0 blocked frames
DEBUG:2025-26-06 10:43:51:Calling <bound method _CallbackResult.signal_once of <pika.adapters.blocking_connection._CallbackResult object at 0x000001BD6F193380>> for "1:Basic.QosOk"
DEBUG:2025-26-06 10:43:51:Recv would block on <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:SelectorIOServicesAdapter.set_writer(1424, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470>>)
DEBUG:2025-26-06 10:43:51:set_writer(1424, _) updated handler RdWr
DEBUG:2025-26-06 10:43:51:Turned on writability watcher: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:Entering blocking state on frame <Basic.Consume(['arguments={}', 'consumer_tag=ctag1.0eecf07c0a9c4770a6c56e23f1b57f8e', 'exclusive=False', 'no_ack=False', 'no_local=False', 'nowait=False', 'queue=ai_dispose', 'ticket=0'])>; acceptable_replies=[(<class 'pika.spec.Basic.ConsumeOk'>, {'consumer_tag': 'ctag1.0eecf07c0a9c4770a6c56e23f1b57f8e'})]
DEBUG:2025-26-06 10:43:51:Adding on_synchronous_complete callback
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method Channel._on_synchronous_complete of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': {'consumer_tag': 'ctag1.0eecf07c0a9c4770a6c56e23f1b57f8e'}, 'calls': 1}
DEBUG:2025-26-06 10:43:51:Adding passed-in RPC response callback
DEBUG:2025-26-06 10:43:51:Added: {'callback': <bound method Channel._on_eventok of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': {'consumer_tag': 'ctag1.0eecf07c0a9c4770a6c56e23f1b57f8e'}, 'calls': 1}
DEBUG:2025-26-06 10:43:51:SelectorIOServicesAdapter.remove_writer(1424)
DEBUG:2025-26-06 10:43:51:remove_writer(1424) updated handler Rd
DEBUG:2025-26-06 10:43:51:Turned off writability watcher: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:43:51:Processing 1:Basic.ConsumeOk
DEBUG:2025-26-06 10:43:51:Processing use of oneshot callback
DEBUG:2025-26-06 10:43:51:0 registered uses left
DEBUG:2025-26-06 10:43:51:Comparing {'consumer_tag': 'ctag1.0eecf07c0a9c4770a6c56e23f1b57f8e'} to {'consumer_tag': 'ctag1.0eecf07c0a9c4770a6c56e23f1b57f8e'}
DEBUG:2025-26-06 10:43:51:Removing callback #1: {'callback': <bound method Channel._on_synchronous_complete of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': {'consumer_tag': 'ctag1.0eecf07c0a9c4770a6c56e23f1b57f8e'}, 'calls': 0}
DEBUG:2025-26-06 10:43:51:Processing use of oneshot callback
DEBUG:2025-26-06 10:43:51:0 registered uses left
DEBUG:2025-26-06 10:43:51:Comparing {'consumer_tag': 'ctag1.0eecf07c0a9c4770a6c56e23f1b57f8e'} to {'consumer_tag': 'ctag1.0eecf07c0a9c4770a6c56e23f1b57f8e'}
DEBUG:2025-26-06 10:43:51:Removing callback #1: {'callback': <bound method Channel._on_eventok of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>>, 'one_shot': True, 'only': None, 'arguments': {'consumer_tag': 'ctag1.0eecf07c0a9c4770a6c56e23f1b57f8e'}, 'calls': 0}
DEBUG:2025-26-06 10:43:51:Calling <bound method _CallbackResult.signal_once of <pika.adapters.blocking_connection._CallbackResult object at 0x000001BD6F80D080>> for "1:Basic.ConsumeOk"
DEBUG:2025-26-06 10:43:51:Calling <bound method Channel._on_synchronous_complete of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>> for "1:Basic.ConsumeOk"
DEBUG:2025-26-06 10:43:51:0 blocked frames
DEBUG:2025-26-06 10:43:51:Calling <bound method Channel._on_eventok of <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>> for "1:Basic.ConsumeOk"
DEBUG:2025-26-06 10:43:51:Discarding frame <METHOD(['channel_number=1', 'frame_type=1', "method=<Basic.ConsumeOk(['consumer_tag=ctag1.0eecf07c0a9c4770a6c56e23f1b57f8e'])>"])>
DEBUG:2025-26-06 10:43:51:Recv would block on <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:44:21:Sending heartbeat frame
DEBUG:2025-26-06 10:44:21:Sending heartbeat frame
DEBUG:2025-26-06 10:44:21:SelectorIOServicesAdapter.set_writer(1424, <bound method _AsyncPlaintextTransport._on_socket_writable of <pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001BD6F710470>>)
DEBUG:2025-26-06 10:44:21:set_writer(1424, _) updated handler RdWr
DEBUG:2025-26-06 10:44:21:Turned on writability watcher: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:44:21:call_later: added timeout <pika.adapters.select_connection._Timeout object at 0x000001BD6F3FB1C0> with deadline=54024.5 and callback=<bound method HeartbeatChecker._send_heartbeat of <pika.heartbeat.HeartbeatChecker object at 0x000001BD6F7F5280>>; now=53994.5; delay=30.0
DEBUG:2025-26-06 10:44:21:SelectorIOServicesAdapter.remove_writer(1424)
DEBUG:2025-26-06 10:44:21:remove_writer(1424) updated handler Rd
DEBUG:2025-26-06 10:44:21:Turned off writability watcher: <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>
DEBUG:2025-26-06 10:44:21:Received heartbeat frame
DEBUG:2025-26-06 10:44:21:Recv would block on <socket.socket fd=1424, family=2, type=1, proto=6, laddr=('127.0.0.1', 56920), raddr=('127.0.0.1', 5672)>

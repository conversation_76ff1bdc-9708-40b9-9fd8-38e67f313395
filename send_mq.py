import asyncio

import aio_pika
import pika


async def send_mq(body=None):
	print("进入异步推送信息--->>>", body)
	connection = await aio_pika.connect_robust(
		f"amqp://guest:guest@127.0.0.1/",
	)

	async with connection:
		routing_key = "ai_dispose"

		channel = await connection.channel()
		await channel.default_exchange.publish(
			aio_pika.Message(body=body.encode("utf-8")),
			routing_key=routing_key,
		)
	print("异步推送成功")


def send_task(payload: dict):
	print("进入发送任务函数~~~")
	connection = pika.BlockingConnection(pika.ConnectionParameters('127.0.0.1'))
	channel = connection.channel()

	queue_name = "ai_dispose"
	# channel.queue_declare(queue=queue_name, durable=True)
	channel.queue_declare(queue=queue_name, durable=True)
	channel.basic_publish(
		exchange='',
		routing_key=queue_name,
		body=payload,
		properties=pika.BasicProperties(
			delivery_mode=2,  # 使消息持久化
		)
	)
	print("✅ 已发送任务：", payload)
	connection.close()


if __name__ == "__main__":
	asyncio.run(send_mq())

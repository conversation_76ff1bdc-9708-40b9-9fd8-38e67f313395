/*!
 * Quasar Framework v1.15.21
 * (c) 2015-present <PERSON><PERSON><PERSON>
 * Released under the MIT License.
 */
!function(t){"function"==typeof define&&define.amd?define(t):t()}(function(){"use strict";function t(t){return"function"==typeof t}"undefined"!=typeof window&&function(t){[Element.prototype,CharacterData.prototype,DocumentType.prototype].forEach(function(t){t.hasOwnProperty("remove")||Object.defineProperty(t,"remove",{configurable:!0,enumerable:!0,writable:!0,value:function(){null!==this.parentNode&&this.parentNode.removeChild(this)}})});try{new MouseEvent("test")}catch(r){var e=function(e,r){r=r||{bubbles:!1,cancelable:!1};var n=document.createEvent("MouseEvent");return n.initMouseEvent(e,r.bubbles,r.cancelable,t,0,r.screenX||0,r.screenY||0,r.clientX||0,r.clientY||0,r.ctrl<PERSON>ey||!1,r.altKey||!1,r.shift<PERSON>ey||!1,r.metaKey||!1,r.button||0,r.relatedTarget||null),n};e.prototype=Event.prototype,t.MouseEvent=e}"function"!=typeof Object.assign&&Object.defineProperty(Object,"assign",{value:function(t,e){var r=arguments;if(null===t||void 0===t)throw new TypeError("Cannot convert undefined or null to object");for(var n=Object(t),o=1;o<arguments.length;o++){var i=r[o];if(null!==i&&void 0!==i)for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&(n[s]=i[s])}return n},writable:!0,configurable:!0}),String.prototype.startsWith||Object.defineProperty(String.prototype,"startsWith",{value:function(t,e){var r=e>0?0|e:0;return this.substring(r,r+t.length)===t}}),String.prototype.endsWith||(String.prototype.endsWith=function(t,e){return(void 0===e||e>this.length)&&(e=this.length),this.substring(e-t.length,e)===t}),Number.isInteger||(Number.isInteger=function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t}),Array.prototype.includes||(Array.prototype.includes=function(t){return!!~this.indexOf(t)}),Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest||(Element.prototype.closest=function(t){var e=this;do{if(e.matches(t))return e;e=e.parentElement||e.parentNode}while(null!==e&&1===e.nodeType);return null}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(t){if(null==this)throw TypeError('"this" is null or not defined');var e=Object(this),r=e.length>>>0;if("function"!=typeof t)throw TypeError("predicate must be a function");for(var n=arguments[1],o=0;o<r;){var i=e[o];if(t.call(n,i,o,e))return i;o++}},configurable:!0,writable:!0}),Array.prototype.findIndex||Object.defineProperty(Array.prototype,"findIndex",{value:function(t){if(null==this)throw new TypeError("Array.prototype.findIndex called on null or undefined");if("function"!=typeof t)throw new TypeError("predicate must be a function");for(var e=Object(this),r=e.length>>>0,n=arguments[1],o=0;o<r;o++)if(t.call(n,e[o],o,e))return o;return-1}}),"classList"in SVGElement.prototype||Object.defineProperty(SVGElement.prototype,"classList",{get:function(){var t=this;return{contains:function(e){return-1!==t.className.baseVal.split(" ").indexOf(e)},add:function(e){return t.setAttribute("class",t.getAttribute("class")+" "+e)},remove:function(e){var r=t.getAttribute("class").replace(new RegExp("(\\s|^)".concat(e,"(\\s|$)"),"g"),"$2");t.classList.contains(e)&&t.setAttribute("class",r)},toggle:function(t){this.contains(t)?this.remove(t):this.add(t)}}}})}(window);var e=Array.isArray?Array.isArray:function(t){return"[object Array]"===Object.prototype.toString.call(t)},r=0,n=void 0,o=void 0,i=function(t,e){h[r]=t,h[r+1]=e,2===(r+=2)&&(o?o(p):m())};var s="undefined"!=typeof window?window:void 0,u=s||{},c=u.MutationObserver||u.WebKitMutationObserver,a="undefined"==typeof self&&"undefined"!=typeof process&&"[object process]"==={}.toString.call(process),l="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel;function f(){var t=setTimeout;return function(){return t(p,1)}}var h=new Array(1e3);function p(){for(var t=0;t<r;t+=2){(0,h[t])(h[t+1]),h[t]=void 0,h[t+1]=void 0}r=0}var d,v,y,b,m=void 0;function _(t,e){var r=this,n=new this.constructor(E);void 0===n[w]&&F(n);var o=r._state;if(o){var s=arguments[o-1];i(function(){return W(o,n,s,r._result)})}else I(r,n,t,e);return n}function g(t){if(t&&"object"==typeof t&&t.constructor===this)return t;var e=new this(E);return P(e,t),e}a?m=function(){return process.nextTick(p)}:c?(v=0,y=new c(p),b=document.createTextNode(""),y.observe(b,{characterData:!0}),m=function(){b.data=v=++v%2}):l?((d=new MessageChannel).port1.onmessage=p,m=function(){return d.port2.postMessage(0)}):m=void 0===s&&"function"==typeof require?function(){try{var t=Function("return this")().require("vertx");return void 0!==(n=t.runOnLoop||t.runOnContext)?function(){n(p)}:f()}catch(t){return f()}}():f();var w=Math.random().toString(36).substring(2);function E(){}var A=void 0,j=1,O=2,S={error:null};function T(t){try{return t.then}catch(t){return S.error=t,S}}function M(e,r,n){r.constructor===e.constructor&&n===_&&r.constructor.resolve===g?function(t,e){e._state===j?C(t,e._result):e._state===O?N(t,e._result):I(e,void 0,function(e){return P(t,e)},function(e){return N(t,e)})}(e,r):n===S?(N(e,S.error),S.error=null):void 0===n?C(e,r):t(n)?function(t,e,r){i(function(t){var n=!1,o=function(t,e,r,n){try{t.call(e,r,n)}catch(t){return t}}(r,e,function(r){n||(n=!0,e!==r?P(t,r):C(t,r))},function(e){n||(n=!0,N(t,e))},t._label);!n&&o&&(n=!0,N(t,o))},t)}(e,r,n):C(e,r)}function P(t,e){var r,n;t===e?N(t,new TypeError("You cannot resolve a promise with itself")):(n=typeof(r=e),null===r||"object"!==n&&"function"!==n?C(t,e):M(t,e,T(e)))}function x(t){t._onerror&&t._onerror(t._result),K(t)}function C(t,e){t._state===A&&(t._result=e,t._state=j,0!==t._subscribers.length&&i(K,t))}function N(t,e){t._state===A&&(t._state=O,t._result=e,i(x,t))}function I(t,e,r,n){var o=t._subscribers,s=o.length;t._onerror=null,o[s]=e,o[s+j]=r,o[s+O]=n,0===s&&t._state&&i(K,t)}function K(t){var e=t._subscribers,r=t._state;if(0!==e.length){for(var n=void 0,o=void 0,i=t._result,s=0;s<e.length;s+=3)n=e[s],o=e[s+r],n?W(r,n,o,i):o(i);t._subscribers.length=0}}function W(e,r,n,o){var i=t(n),s=void 0,u=void 0,c=void 0,a=void 0;if(i){if((s=function(t,e){try{return t(e)}catch(t){return S.error=t,S}}(n,o))===S?(a=!0,u=s.error,s.error=null):c=!0,r===s)return void N(r,new TypeError("A promises callback cannot return that same promise."))}else s=o,c=!0;r._state!==A||(i&&c?P(r,s):a?N(r,u):e===j?C(r,s):e===O&&N(r,s))}var Y=0;function F(t){t[w]=id++,t._state=void 0,t._result=void 0,t._subscribers=[]}var L=function(){function t(t,r){this._instanceConstructor=t,this.promise=new t(E),this.promise[w]||F(this.promise),e(r)?(this.length=r.length,this._remaining=r.length,this._result=new Array(this.length),0===this.length?C(this.promise,this._result):(this.length=this.length||0,this._enumerate(r),0===this._remaining&&C(this.promise,this._result))):N(this.promise,new Error("Array Methods must be provided an Array"))}return t.prototype._enumerate=function(t){for(var e=0;this._state===A&&e<t.length;e++)this._eachEntry(t[e],e)},t.prototype._eachEntry=function(t,e){var r=this._instanceConstructor,n=r.resolve;if(n===g){var o=T(t);if(o===_&&t._state!==A)this._settledAt(t._state,e,t._result);else if("function"!=typeof o)this._remaining--,this._result[e]=t;else if(r===k){var i=new r(E);M(i,t,o),this._willSettleAt(i,e)}else this._willSettleAt(new r(function(e){return e(t)}),e)}else this._willSettleAt(n(t),e)},t.prototype._settledAt=function(t,e,r){var n=this.promise;n._state===A&&(this._remaining--,t===O?N(n,r):this._result[e]=r),0===this._remaining&&C(n,this._result)},t.prototype._willSettleAt=function(t,e){var r=this;I(t,void 0,function(t){return r._settledAt(j,e,t)},function(t){return r._settledAt(O,e,t)})},t}();var k=function(){function t(e){this[w]=Y++,this._result=this._state=void 0,this._subscribers=[],E!==e&&("function"!=typeof e&&function(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}(),this instanceof t?function(t,e){try{e(function(e){P(t,e)},function(e){N(t,e)})}catch(e){N(t,e)}}(this,e):function(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}())}return t.prototype.catch=function(t){return this.then(null,t)},t.prototype.finally=function(t){var e=this.constructor;return this.then(function(r){return e.resolve(t()).then(function(){return r})},function(r){return e.resolve(t()).then(function(){throw r})})},t}();k.prototype.then=_,k.all=function(t){return new L(this,t).promise},k.race=function(t){var r=this;return e(t)?new r(function(e,n){for(var o=t.length,i=0;i<o;i++)r.resolve(t[i]).then(e,n)}):new r(function(t,e){return e(new TypeError("You must pass an array to race."))})},k.resolve=g,k.reject=function(t){var e=new this(E);return N(e,t),e},k._setScheduler=function(t){o=t},k._setAsap=function(t){i=t},k._asap=i,function(){var t=void 0;if("undefined"!=typeof global)t=global;else if("undefined"!=typeof self)t=self;else try{t=Function("return this")()}catch(t){throw new Error("polyfill failed because global object is unavailable in this environment")}var e=t.Promise;if(e){var r=null;try{r=Object.prototype.toString.call(e.resolve())}catch(t){}if("[object Promise]"===r&&!e.cast)return}t.Promise=k}()});
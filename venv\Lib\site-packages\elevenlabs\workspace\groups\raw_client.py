# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import <PERSON><PERSON>NDecodeError

from ...core.api_error import ApiError
from ...core.client_wrapper import Async<PERSON>lient<PERSON>rapper, SyncClientWrapper
from ...core.http_response import As<PERSON><PERSON>tt<PERSON><PERSON><PERSON>ponse, HttpResponse
from ...core.request_options import RequestOptions
from ...core.unchecked_base_model import construct_type
from ...errors.unprocessable_entity_error import UnprocessableEntityError
from ...types.http_validation_error import HttpValidationError
from ...types.workspace_group_by_name_response_model import WorkspaceGroupByNameResponseModel


class RawGroupsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def search(
        self, *, name: str, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[typing.List[WorkspaceGroupByNameResponseModel]]:
        """
        Searches for user groups in the workspace. Multiple or no groups may be returned.

        Parameters
        ----------
        name : str
            Name of the target group.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[typing.List[WorkspaceGroupByNameResponseModel]]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/workspace/groups/search",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "name": name,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    typing.List[WorkspaceGroupByNameResponseModel],
                    construct_type(
                        type_=typing.List[WorkspaceGroupByNameResponseModel],  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawGroupsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def search(
        self, *, name: str, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[typing.List[WorkspaceGroupByNameResponseModel]]:
        """
        Searches for user groups in the workspace. Multiple or no groups may be returned.

        Parameters
        ----------
        name : str
            Name of the target group.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[typing.List[WorkspaceGroupByNameResponseModel]]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/workspace/groups/search",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "name": name,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    typing.List[WorkspaceGroupByNameResponseModel],
                    construct_type(
                        type_=typing.List[WorkspaceGroupByNameResponseModel],  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

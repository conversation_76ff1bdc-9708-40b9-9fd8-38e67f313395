import json
import os
import subprocess
import time

import cv2
from flask import Flask, request, send_file, Response, render_template
from flask_cors import CORS
from flask_redis import FlaskRedis

from extensions import init_celery
from send_mq import send_task
from celery_task import *

app = Flask(__name__)
CORS(app)
app.config["IP"] = "127.0.0.1"
app.config["PORT"] = 8888
app.config.update(
	REDIS_URL=f"redis://:3176383@{app.config['IP']}:6379/0",
	CELERY_BROKER_URL=f"redis://:3176383@{app.config['IP']}:6379/2",
	CELERY_RESULT_BACKEND=f"redis://:3176383@{app.config['IP']}:6379/2"
)
redis_client = FlaskRedis(app)
celery = init_celery(app)  # 初始化 Celery


# 上传文件 type(1:上传音视频 2:上传文本)
@app.route('/ai/upload', methods=["POST"])
def receive_message():
	# 获取上传的文件 以及 form 表单信息
	file = request.files
	task_id = request.form.get("taskId")
	original_lang = request.form.get("original_lang")
	translate_lang = request.form.get("translate_lang")
	is_enabled = request.form.get("is_enabled")  # （on：开）（off：关）
	is_enabled_lipsync = request.form.get("is_enabled_lipsync")  # （on：开）（off：关）

	# 校验文件是否存在
	if not all([file, task_id, original_lang, translate_lang, is_enabled, is_enabled_lipsync]):
		return {
			"code": 400,
			"status": "missing parameters"
		}

	# 创建任务信息 并 存储到redis数据库
	set_redis_dict = {
		"taskId": task_id,
		"create_time": str(int(time.time())),
		"image": "",
		"video": "",
		"original_lang": original_lang,
		"original_text": [],
		"translate_lang": translate_lang,
		"translate_text": "",
		"audio": "",
		"lipsync": "",
		"status": {
			"code": "1",
			"statu": "pending"
		}}
	# 将任务状态推入队列
	try:
		redis_client.set(task_id, json.dumps(set_redis_dict, ensure_ascii=False))
	except Exception as e:
		return {
			"code": 500,
			"status": f"task create error reason is {e}"
		}

	# 将接收到的文件存储到allData/upload文件下
	try:
		init_path = f"./allData/upload/{task_id}"
		if not os.path.exists(init_path):
			os.mkdir(init_path)
		file['file'].save(os.path.join(init_path, file['file'].filename))

		# 保存视频第一针画面作为图片
		# 打开视频文件
		cap = cv2.VideoCapture(os.path.join(init_path, file['file'].filename))
		# 获取第一帧
		ret, frame = cap.read()
		# 保存第一帧为图片
		cv2.imwrite(os.path.join(init_path, f"{task_id}.jpg"), frame)
		# 释放资源
		cap.release()

		# 更新状态
		set_redis_dict["image"] = f"http://{app.config['IP']}:{app.config["PORT"]}/ai/get/image?taskId={task_id}"
		set_redis_dict["video"] = f"http://{app.config['IP']}:{app.config["PORT"]}/ai/get/video?taskId={task_id}"
		set_redis_dict["status"]["statu"] = "success"

		try:
			redis_client.set(task_id, json.dumps(set_redis_dict, ensure_ascii=False))
		except Exception as e:
			return {
				"code": 500,
				"status": f"task create error reason is {e}"
			}
		# 开始异步处理视频
		# task = dispose_video.delay(task_id, original_lang, translate_lang, set_redis_dict, is_enabled,
		# 						   is_enabled_lipsync)
		new_dict = {
			"taskId": task_id,
			"original_lang": original_lang,
			"language": translate_lang,
			"is_enabled": is_enabled,
			"is_enabled_lipsync": is_enabled_lipsync,
			"dispose_type": "dispose_video",
			"task_dict": set_redis_dict
		}
		print("new_dict：", new_dict)
		# 开始异步处理视频
		# generate_clone.delay(task_id, language)
		# asyncio.run(send_mq(json.dumps(new_dict)))
		send_task(json.dumps(new_dict))

	except Exception as e:
		return {
			"code": 500,
			"statu": f"failed to save the file reason is {e}"
		}

	return {"code": 200, "statu": "Success"}


# 音视频翻译 - 更新内容
@app.route('/ai/upload/text', methods=["POST"])
def upload_text():
	# 1、获取需要传入的参数
	taskId = request.json["taskId"]
	updateList = request.json["updateList"]
	# 1：修改原内容 2：修改翻译后的内容
	type = request.json["type"]

	# 2、校验传入的参数是否有缺失
	if not all([taskId, updateList, type]):
		return {
			"statusCode": 400,
			"statusMessage": "Missing parameters",
		}

	# 3、读取内容
	try:
		if int(type) == 1:
			fileUrl = f"./allData/output/{taskId}/tran.vtt"
		elif int(type) == 2:
			fileUrl = f"./allData/output/{taskId}/translate.vtt"
		else:
			return {
				"statusCode": 400,
				"statusMessage": "Type does not exist ",
			}

		with open(fileUrl, 'w', encoding='utf-8') as vtt_file:
			# 写入VTT文件头
			vtt_file.write("WEBVTT\n\n")

			# 遍历JSON数据
			for index, entry in enumerate(updateList, start=1):
				start_time = entry["StartTime"]
				end_time = entry["EndTime"]
				text = entry["text"]

				# 写入序号
				vtt_file.write(f"{index}\n")
				# 写入时间戳
				vtt_file.write(f"{start_time} --> {end_time}\n")
				# 写入字幕文本
				vtt_file.write(f"{text}\n\n")
		return {
			"statusCode": 200,
			"statusMessage": "Success",
		}
	except Exception as e:
		return {
			"statusCode": 500,
			"statusMessage": e,
		}


# 音视频翻译 - 内容翻译
@app.route('/ai/translate', methods=["POST"])
def translate():
	# 1、获取需要传入的参数
	json_data = request.json
	task_id = json_data.get("taskId")
	language = json_data.get("language")
	# 2、校验传入的参数是否有缺失
	if not all([task_id, language]):
		return {
			"statusCode": 400,
			"statusMessage": "Missing parameters",
		}

	# 3、判断预翻译的VTT文件是否已经存在
	if not os.path.exists(f"./allData/output/{task_id}/tran.vtt"):
		return {
			"statusCode": 400,
			"statusMessage": "File not found",
		}
	# 4、将内容推入异步进行处理
	json_data["dispose_type"] = "ai_translate"
	try:
		send_task(json.dumps(json_data))
		# asyncio.run(send_mq(json.dumps(json_data)))
		return {"code": 200, "status": "Success"}
	except Exception as e:
		return {"code": 500, "status": f"translate task create error reason is {e}"}


# 生成音频
@app.route('/ai/clone', methods=["POST"])
def clone():
	# 1、获取必要的上传数据
	all_data = request.json
	task_id = all_data.get("taskId")
	language = all_data.get("language")

	# 2、校验必要的参数是否缺失
	if not all([task_id, language]):
		return {
			"statusCode": 400,
			"statusMessage": "Missing parameters",
		}
	try:
		# 开始异步处理视频
		new_dict = {
			"dispose_type": "generate_clone",
			"taskId": task_id,
			"language": language
		}
		# generate_clone.delay(task_id, language)
		send_task(json.dumps(new_dict))
		# asyncio.run(send_mq(json.dumps(new_dict)))
		return {"code": 200, "status": "Success"}
	except Exception as e:
		return {"code": 200, "status": f"fail reason is {e}"}


@app.route('/ai/get/<type>', methods=["GET"])
def get_file(type):
	task_id = request.args.get("taskId")
	if not task_id:
		return {
			"statusCode": 400,
			"statusMessage": "Missing parameters",
		}
	if type == "audio":
		if os.path.exists(f"./allData/output/{task_id}/el-1.wav"):
			path = f"./allData/output/{task_id}/el-1.wav"
		else:
			path = f"./allData/output/{task_id}/el.wav"
		return send_file(path, as_attachment=False)

	elif type == "image":
		with open(f"./allData/upload/{task_id}/{task_id}.jpg", 'rb') as img_f:
			img_stream = img_f.read()

		return Response(img_stream, mimetype='image/jpeg')

	elif type == "video":
		if os.path.exists(f"./allData/upload/{task_id}/{task_id}.mp4"):
			path = f"./allData/upload/{task_id}/{task_id}.mp4"
			return send_file(path, as_attachment=False)
		else:
			return {
				"code": "404",
				"status": "task id not found"
			}

	elif type == "lipsync":
		if os.path.exists(f"./allData/output/{task_id}/lipsync.mp4"):
			path = f"./allData/output/{task_id}/lipsync.mp4"
			return send_file(path, as_attachment=False)
		else:
			return {
				"code": "404",
				"status": "task id not found"
			}

	else:
		return {
			"code": "500",
			"status": "type fail"
		}


# 获取任务队列数据
@app.route('/ai/task/list', methods=["GET"])
def get_task_list():
	print("进入获取task_list")
	task_id = request.args.get("taskId")
	page = int(request.args.get("page", 1))
	page_size = 7
	if task_id:
		task_dict = redis_client.get(task_id)
		if not task_dict:
			return {"code": 404, "status": "this task id not found"}
		return task_dict

	# 1、进入redis 获取队列数据库里的所有请求
	all_task_key = redis_client.keys()
	all_task_list = []
	for i in all_task_key:
		all_task_list.append(json.loads(redis_client.get(i)))

	# 按创建时间排序（假设任务中有 'create_time' 字段，格式为时间戳或ISO格式字符串）
	all_task_list.sort(key=lambda x: x.get("create_time", ""), reverse=True)

	# 分页处理
	start = (page - 1) * page_size
	end = start + page_size
	paged_tasks = all_task_list[start:end]
	response = {
		"total": len(all_task_key),
		"task_list": paged_tasks}

	print("返回的response：", response)
	return response


# 获取任务队列数据
@app.route('/ai/task/delete', methods=["POST"])
def delete_task():
	task_id = request.json.get("taskId")
	if not task_id:
		return {"code": 404, "status": "this task id not found"}
	try:
		# 1、删除redis缓存数据
		redis_client.delete(task_id)

		# 2、删除本地的所有资料
		upload_path = f"./allData/upload/{task_id}"
		output_path = f".allData/output/{task_id}"
		if os.path.exists(upload_path):
			try:
				subprocess.run(['cmd.exe', '/ta', 'del', upload_path], check=True)
				print("upload file deleted successfully")
			except subprocess.CalledProcessError as e:
				print(f"删除失败: {e}")

		if os.path.exists(upload_path):
			try:
				subprocess.run(['cmd.exe', '/ta', 'del', output_path], check=True)
				print("upload file deleted successfully")
			except subprocess.CalledProcessError as e:
				print(f"删除失败: {e}")

		return {"code": 200, "status": "success"}
	except Exception as e:
		return {"code": 500, "status": f"task delete failed reason is {e}"}


@app.route('/ai/lipsync', methods=["POST"])
def run_lipsync_video():
	task_id = request.json.get("taskId")
	if not task_id:
		return {
			"statusCode": 400,
			"statusMessage": "Missing parameters",
		}

	source_url = f"./allData/output/{task_id}/el.wav"
	target_url = f"./allData/upload/{task_id}/{task_id}.mp4"

	if not os.path.exists(source_url) and os.path.exists(target_url):
		return {
			"statusCode": 400,
			"statusMessage": "Missing required parameters",
		}
	try:
		new_dict = {
			"taskId": task_id,
			"source_url": source_url,
			"target_url": target_url,
			"dispose_type": "run_lipsync",
		}
		# 开始异步处理视频
		# run_lipsync.delay(task_id, source_url, target_url)
		send_task(json.dumps(new_dict))
		# asyncio.run(send_mq(json.dumps(new_dict)))
		return {"code": 200, "status": "Success"}
	except Exception as e:
		return {"code": 500, "status": f"task delete failed reason is {e}"}


if __name__ == '__main__':
	app.run(host='0.0.0.0', port=8888, debug=True)

# This file was auto-generated by Fern from our API Definition.

from __future__ import annotations

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2, update_forward_refs
from ..core.unchecked_base_model import UncheckedBaseModel
from .dynamic_variables_config import DynamicVariablesConfig


class ClientToolConfigInput(UncheckedBaseModel):
    """
    A client tool is one that sends an event to the user's client to trigger something client side
    """

    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = pydantic.Field(default=None)
    """
    The maximum time in seconds to wait for the tool call to complete. Must be between 1 and 30 seconds (inclusive).
    """

    parameters: typing.Optional["ObjectJsonSchemaPropertyInput"] = pydantic.Field(default=None)
    """
    Schema for any parameters to pass to the client
    """

    expects_response: typing.Optional[bool] = pydantic.Field(default=None)
    """
    If true, calling this tool should block the conversation until the client responds with some response which is passed to the llm. If false then we will continue the conversation without waiting for the client to respond, this is useful to show content to a user but not block the conversation
    """

    dynamic_variables: typing.Optional[DynamicVariablesConfig] = pydantic.Field(default=None)
    """
    Configuration for dynamic variables
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


from .array_json_schema_property_input import ArrayJsonSchemaPropertyInput  # noqa: E402, F401, I001
from .object_json_schema_property_input import ObjectJsonSchemaPropertyInput  # noqa: E402, F401, I001

update_forward_refs(ClientToolConfigInput)

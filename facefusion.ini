[paths]
temp_path =
jobs_path =
source_paths =
target_path =
output_path =

[patterns]
source_pattern =
target_pattern =
output_pattern =

[face_detector]
face_detector_model =
face_detector_size =
face_detector_angles =
face_detector_score =

[face_landmarker]
face_landmarker_model =
face_landmarker_score =

[face_selector]
face_selector_mode =
face_selector_order =
face_selector_age_start =
face_selector_age_end =
face_selector_gender =
face_selector_race =
reference_face_position =
reference_face_distance =
reference_frame_number =

[face_masker]
face_occluder_model =
face_parser_model =
face_mask_types =
face_mask_blur =
face_mask_padding =
face_mask_regions =

[frame_extraction]
trim_frame_start =
trim_frame_end =
temp_frame_format =
keep_temp =

[output_creation]
output_image_quality =
output_image_resolution =
output_audio_encoder =
output_audio_quality =
output_audio_volume =
output_video_encoder =
output_video_preset =
output_video_quality =
output_video_resolution =
output_video_fps =

[processors]
processors =
age_modifier_model =
age_modifier_direction =
deep_swapper_model =
deep_swapper_morph =
expression_restorer_model =
expression_restorer_factor =
face_debugger_items =
face_editor_model =
face_editor_eyebrow_direction =
face_editor_eye_gaze_horizontal =
face_editor_eye_gaze_vertical =
face_editor_eye_open_ratio =
face_editor_lip_open_ratio =
face_editor_mouth_grim =
face_editor_mouth_pout =
face_editor_mouth_purse =
face_editor_mouth_smile =
face_editor_mouth_position_horizontal =
face_editor_mouth_position_vertical =
face_editor_head_pitch =
face_editor_head_yaw =
face_editor_head_roll =
face_enhancer_model =
face_enhancer_blend =
face_enhancer_weight =
face_swapper_model =
face_swapper_pixel_boost =
frame_colorizer_model =
frame_colorizer_size =
frame_colorizer_blend =
frame_enhancer_model =
frame_enhancer_blend =
lip_syncer_model =

[uis]
open_browser =
ui_layouts =
ui_workflow =

[execution]
execution_device_id =
execution_providers =
execution_thread_count =
execution_queue_count =

[download]
download_providers =
download_scope =

[memory]
video_memory_strategy =
system_memory_limit =

[misc]
log_level =
halt_on_error =

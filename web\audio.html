<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Audio Translate</title>
    <link href="./css/quasar/icon.css" rel="stylesheet" />
    <link href="./css/quasar/fonts-css.css" rel="stylesheet" type="text/css" />
    <link
      href="./css/quasar/animate.min.css"
      rel="stylesheet"
      type="text/css"
    />
    <link href="./css/quasar/quasar.min.css" rel="stylesheet" type="text/css" />
    <link href="./css/index.css" rel="stylesheet" type="text/css" />
    <script src="./plugin/vue.min.js"></script>
    <script src="./plugin/jquery-3.2.1.min.js"></script>
    <script src="./js/env.js"></script>
  </head>
  <body>
    <div id="el-stop-1" v-cloak>
      <div
        class="audio-container text-center"
        style="display: flex; flex-direction: column; gap: 20px"
      >
        <div>
          <div
            class="col-xs-12 col-md-12 text-h5 text-primary text-center q-pt-sm"
          >
            文件內容類型
          </div>
          <div>
            <q-btn-toggle
              v-model="type"
              spread
              no-caps
              toggle-color="primary"
              color="white"
              text-color="primary"
              :options="[
          {label: '純文本', value: '1'},
          {label: 'VTT文本', value: '2'}
        ]"
            />
          </div>
        </div>
        <div v-if="type==='2'">
          <div
            class="col-xs-12 col-md-12 text-h5 text-primary text-center q-pt-sm"
          >
            導入VTT文件
          </div>
          <q-btn color="primary" no-caps @click="onUploadVtt"
            >上傳VTT文件
            <input
              ref="inputVttRef"
              style="display: none"
              type="file"
              id="file"
              name="file"
              accept="text/vtt"
              @change="onVttChange"
            />
          </q-btn>
        </div>
        <div>
          <div style="font-size: 32px">輸入文字:</div>
          <q-input
            outlined
            v-model="audioValue"
            label="Audio Text"
            type="textarea"
          />
        </div>
        <div class="row">
          <div
            class="col-xs-12 col-md-12 text-h5 text-primary text-center q-pt-sm"
          >
            Current Audio text Language
          </div>
          <div class="col-xs-12 col-md-12 shadow-1 q-mt-md q-pa-sm">
            <div class="q-gutter-sm row justify-center">
              <q-radio
                v-for="(item, index) in langOption"
                :key="index"
                v-model="langObj.to"
                :val="item"
                :label="item"
              >
              </q-radio>
            </div>
          </div>
        </div>
        <div class="row justify-center">
          <div class="column">
            <label for="file">选择要上传的VTT文件</label
            ><input
              ref="inputAudioRef"
              type="file"
              id="file"
              name="file"
              accept="audio/*,video/*"
              @change="onAudioChange"
            />
          </div>
          <q-btn color="primary" no-caps @click="onUploadAudio">上傳音頻</q-btn>
        </div>
        <div>
          <q-btn color="primary" no-caps @click="onText2BAudio">提交</q-btn>
        </div>
        <div v-if="audioAddress">
          <div style="font-size: 32px">輸出音頻:</div>
          <audio :src="audioAddress" controls />
        </div>
      </div>
      <q-inner-loading :showing="loading">
        <q-spinner-ball size="50px" color="primary"></q-spinner-ball>
        <h5 class="q-mt-lg">處理中...</h5>
      </q-inner-loading>
    </div>
  </body>
  <script>
    window.quasarConfig = {
      brand: {
        primary: "#014aa1",
        secondary: "#26A69A",
        accent: "#9C27B0",

        dark: "#1d1d1d",

        positive: "#21BA45",
        negative: "#C10015",
        info: "#31CCEC",
        warning: "#F2C037",
      },
      loadingBar: { skipHijack: true },
    };
  </script>
  <script src="./plugin/quasar.umd.min.js"></script>
  <script src="./plugin/quasar.ie.polyfills.umd.min.js"></script>
  <script src="./css/quasar/zh-hant.umd.min.js"></script>
  <script>
    Quasar.lang.set(Quasar.lang.zhHant);
  </script>

  <script>
    new Vue({
      el: "#el-stop-1",
      data: function () {
        return {
          // 2024-01-13
          loading: false,
          audioValue: "",
          type: "1",
          langOption: [
            "中文",
            "粵語",
            "英文",
            "越南語",
            "日語",
            "印尼語",
            "高棉語",
            "泰語",
            "阿拉伯文",
            "法文",
            "西班牙文",
            "马来文",
            "韩语",
          ],
          langObj: {
            form: "zh",
            to: "中文",
          },
          audioFile: null,
          audioAddress: "",
        };
      },
      watch: {
        type: function (newValue) {
          this.audioValue = "";
        },
      },
      methods: {
        // text translate audio
        onAudioChange(e) {
          const file = e.target.files[0];
          const fileSuffix = file.name.split(".")[1];
          const timestamp = String(Date.now());
          const newFileName = `${timestamp}.${fileSuffix}`;
          const newFile = new File([file], newFileName, { type: file.type });
          this.taskId = timestamp;
          this.audioFile = newFile;
          return;
        },
        onUploadAudio() {
          const _this = this;
          const { type, taskId, audioFile } = _this;
          if (!audioFile) {
            _this.notifyCustom("請選擇上傳音頻", "red");
            return;
          }
          _this.loading = true;
          const formData = new FormData();
          formData.append("taskId", String(taskId));
          formData.append("file", audioFile);

          $.ajax({
            url: `${PUBLIC_BASE_URL}/ai/upload/1`,
            method: "POST",
            data: formData,
            timeout: 1000 * 60,
            contentType: false,
            processData: false,
            // headers: { "content-type": "application/json" },
            success: function (res) {
              console.log("onUploadAudio res", res);
              if (res.statusCode === 200) {
                _this.notifyCustom("成功", "positive");
              } else {
                _this.notifyCustom("失败", "red");
              }
            },
            error: function (err) {
              _this.notifyCustom("失败", "red");
            },
            complete: function () {
              _this.loading = false;
            },
          });
        },
        onText2BAudio() {
          const _this = this;
          const { audioValue, type, audioFile, taskId } = _this;
          if (!audioFile) {
            _this.notifyCustom("請選擇上傳音頻", "red");
            return;
          }
          if (!audioValue.trim()) {
            _this.notifyCustom("請輸入內容轉換音頻", "red");
            return;
          }
          _this.loading = true;
          let data = {
            taskId,
            type,
            // file:null,
          };
          if (type === "1") {
            data = {
              ...data,
              text: audioValue,
            };
          } else {
            data = {
              ...data,
              vtt: audioValue,
            };
          }

          $.ajax({
            url: `${PUBLIC_BASE_URL}/ai/upload/2`,
            method: "POST",
            data: JSON.stringify(data),
            timeout: 1000 * 60,
            headers: { "content-type": "application/json" },
            success: function (res) {
              console.log("onText2BAudio res", res);
              if (res.statusCode === 200) {
                _this.notifyCustom("成功", "positive");
                _this.onGetAudioAddress();
              } else {
                _this.notifyCustom("失败", "red");
              }
            },
            error: function (err) {
              _this.notifyCustom("失败", "red");
              _this.loading = false;
            },
          });
        },
        onGetAudioAddress() {
          const _this = this;
          const { taskId, langObj } = _this;

          const data = {
            taskId,
            language: langObj.to,
          };
          _this.loading = true;
          $.ajax({
            url: `${PUBLIC_BASE_URL}/ai/genAudio`,
            method: "POST",
            data: JSON.stringify(data),
            timeout: 1000 * 60 * 5,
            headers: { "content-type": "application/json" },
            success: function (res) {
              console.log("onGetAudioAddress res", res);
              if (res.statusCode === 200) {
                _this.audioAddress = res.audioUrl;
                _this.$refs.inputAudioRef.value = "";
                _this.notifyCustom("成功", "positive");
              } else {
                _this.notifyCustom("失败", "red");
              }
            },
            error: function (err) {
              _this.notifyCustom("失败", "red");
            },
            complete: function () {
              _this.loading = false;
            },
          });
        },
        // import VTT File write value
        onUploadVtt() {
          this.$refs.inputVttRef.click();
        },
        onVttChange(e) {
          const _this = this;
          const file = e.target.files[0];
          if (file) {
            const reader = new FileReader();
            reader.readAsText(file, "UTF-8");
            reader.onload = function (event) {
              const sileString = event.target.result;
              _this.audioValue = sileString;
              _this.$refs.inputVttRef.value = ''
            };
          }
        },
      },
      mounted() {},
      beforeDestroy() {
        console.log("離開...");
      },
    });
  </script>
</html>

import asyncio
import time

import webvtt
from openai import AsyncOpenAI

from config import Config


def time_to_seconds(time_str):
	# 将字符串转换为秒
	h, m, s = time_str.split(':')
	total_seconds = int(h) * 3600 + int(m) * 60 + float(s)
	return total_seconds


# 📚 翻译（控制翻译长度适合原语音时长）
async def translate_text(text, original_text, target_languages, original_duration):
	prompt = (
		f"将以下{original_text}翻译成{target_languages}，请保证翻译内容适合在{original_duration:.1f}秒内自然朗读，"
		f"不要太简短，也不要冗长。原文：{text}"
	)
	print("prompt：", prompt)
	try:
		async with AsyncOpenAI(api_key=Config.openAI_key) as client:
			completion = await client.chat.completions.create(
				model="gpt-4o",
				messages=[
					{"role": "user", "content": prompt}
				],
				temperature=0.7
			)
		return completion.choices[0].message.content
	except Exception as e:
		print(f"[翻译失败] {e}")
		return text  # fallback


async def asyncio_dispose(taskId, original_text, target_languages, stat_time, end_time, number):
	time1_seconds = time_to_seconds(str(stat_time))
	time2_seconds = time_to_seconds(str(end_time))

	# 计算秒差
	original_duration = time2_seconds - time1_seconds

	# 执行翻译
	response_translate = await translate_text(original_text, original_text, target_languages, original_duration)
	print("response_translate：", response_translate)

	return {"start": str(stat_time), "end": str(end_time), "text": response_translate,
			"number": number}


async def process_audio(taskId, target_languages):
	audio_path = f"./allData/output/{taskId}/tran.vtt"
	captions = webvtt.read(audio_path)
	number = 1
	task_list = []
	for caption in captions:
		print("caption：", caption)
		original_text = caption.text
		stat_time = caption.start
		end_time = caption.end
		print(stat_time, end_time)
		task_list.append(asyncio_dispose(taskId, original_text, target_languages, stat_time, end_time, number))
		number += 1

	all_save_vtt_texts = await asyncio.gather(*task_list)
	print("翻译处理完毕---》》》", all_save_vtt_texts)
	sorted_data = sorted(all_save_vtt_texts, key=lambda x: x['number'])
	print("sorted_data：", sorted_data)

	return sorted_data


# 示例用法
if __name__ == "__main__":
	start_time = time.time()
	asyncio.run(process_audio("ID17482265182813146", "中文"))
	print("总耗时：", time.time() - start_time)

# 合成语音并检查时长
# await narakeetRun(type="text", data=response_translate, taskID=taskId, language=target_languages, number=number)
# synthesized_duration = AudioFileClip(f"./allData/output/{taskId}/{number}.wav").duration

# print(f"原始时间：{original_duration} 翻译后的时间：{synthesized_duration}")

# num = 1
# # 如果合成语音时长与原始音频时长相差较大，进行调整
# while abs(synthesized_duration - original_duration) > 0.5 and num <= 3 and len(translate_text) > 2:
# 	print(f"重试次数：{num}")
# 	# 缩短翻译文本
# 	if synthesized_duration - original_duration > 0:
# 		if synthesized_duration - original_duration > 8:
# 			state = "减少五个字"
# 		else:
# 			state = "减少一个字"
#
# 	else:
# 		state = "增加十个字"
# 	try:
# 		for i in range(3):
# 			translate_text = await retry_translate_text_openai(translate_text, synthesized_duration,
# 															   original_duration,
# 															   state)
# 			if translate_text != "Request timed out.":
# 				break
# 	except Exception as e:
# 		print("处理时间- openai 出现错误-->>>", e)
# 	print("response_translate：", translate_text)
# 	# 重新合成语音
# 	try:
# 		await narakeetRun(type="text", data=translate_text, taskID=taskId, language=target_languages, number=number)
# 	except Exception as e:
# 		print("处理时间- narakeetRun 出现错误-->>>", e)
# 	synthesized_duration = AudioFileClip(f"./allData/output/{taskId}/{number}.wav").duration
# 	num += 1
# 	print(f"{number} 原时长 {original_duration}秒，时长为 {synthesized_duration} 秒。")
# all_save_vtt_texts.append(
# 	{"start": str(stat_time), "end": str(end_time), "text": response_translate,
# 	 "number": number})

import hashlib
from typing import List, Optional

import numpy

from facefusion.types import Face, FaceSet, FaceStore, VisionFrame

FACE_STORE : FaceStore =\
{
	'static_faces': {},
	'reference_faces': {}
}


def get_face_store() -> FaceStore:
	return FACE_STORE


def get_static_faces(vision_frame : VisionFrame) -> Optional[List[Face]]:
	frame_hash = create_frame_hash(vision_frame)
	if frame_hash in FACE_STORE['static_faces']:
		return FACE_STORE['static_faces'][frame_hash]
	return None


def set_static_faces(vision_frame : VisionFrame, faces : List[Face]) -> None:
	frame_hash = create_frame_hash(vision_frame)
	if frame_hash:
		FACE_STORE['static_faces'][frame_hash] = faces


def clear_static_faces() -> None:
	FACE_STORE['static_faces'] = {}


def create_frame_hash(vision_frame : VisionFrame) -> Optional[str]:
	if numpy.any(vision_frame):
		frame_hash = hashlib.blake2b(vision_frame.tobytes(), digest_size = 16).hexdigest()
		return frame_hash
	return None


def get_reference_faces() -> Optional[FaceSet]:
	if FACE_STORE['reference_faces']:
		return FACE_STORE['reference_faces']
	return None


def append_reference_face(name : str, face : Face) -> None:
	if name not in FACE_STORE['reference_faces']:
		FACE_STORE['reference_faces'][name] = []
	FACE_STORE['reference_faces'][name].append(face)


def clear_reference_faces() -> None:
	FACE_STORE['reference_faces'] = {}

import asyncio
import json
import logging

import aio_pika

from utils.task_generate_clone_audio import generate_clone_audio
from utils.task_init_dispose_video import dispose_video
from task_lipsync import lipsync
from utils.translate import process_vtt

logging.basicConfig(level=logging.INFO)


async def process_message(message: aio_pika.abc.AbstractIncomingMessage) -> None:
	# async with ((message.process())):
	# async def runner():
	async with message.process():  # 自动在完成前 ack
		try:
			body = json.loads(message.body)
			dispose_type = body.get("dispose_type")
			print("获取到的dispose_type:", dispose_type)
			task_id = body.get("taskId")
			target_languages = body.get("language")
			if dispose_type == "dispose_video":
				print("正在执行 --- dispose_video ---- ")
				original_lang = body.get("original_lang")
				task_dict = body.get("task_dict")
				is_enabled = body.get("is_enabled")
				# is_enabled_lipsync = body.get("is_enabled_lipsync")
				dispose_video(task_id, original_lang, task_dict)
				print("开始执行任务三~~~")
				# 执行任务3 --- 将提取到的内容更新到任务字典里'
				try:
					print(f"🚀 开始异步处理 task: {task_id}")
					run_state = await process_vtt(task_id, target_languages)
					print(f"✅ task: {task_id} 成功 ack")
					print("执行翻译返回的内容：", run_state)
				except asyncio.CancelledError:
					logging.error("⚠️ process_vtt 被取消（CancelledError）")
					# 可视需求选择 nack 或忽略
					raise
				except Exception as e:
					logging.exception("❌ 任务处理异常：%s", e)
				if is_enabled == "on" and run_state == "success":
					try:
						# 开始异步处理视频
						await generate_clone_audio(task_id, target_languages)
					except Exception as e:
						print("task 4 dispose status failed reason is {}".format(e))

			elif dispose_type == "ai_translate":
				print("正在执行 --- ai_translate ---- ")
				await process_vtt(task_id, target_languages)

			elif dispose_type == "generate_clone":
				print("正在执行 --- generate_clone ---- ")
				await generate_clone_audio(task_id, target_languages)

			elif dispose_type == "run_lipsync":
				print("正在执行 --- run_lipsync ---- ")
				source_url = body.get("source_url")
				target_url = body.get("target_url")
				lipsync(task_id, source_url, target_url)
		except Exception as e:
			logging.exception("❌ 消息处理过程中发生异常：%s", e)
			# 这里 raise 会让 aio_pika 自动 nack（除非 auto_ack）
			raise


async def main() -> None:
	connection = await aio_pika.connect_robust(
		f"amqp://guest:guest@127.0.0.1/",
	)
	# Creating channel
	channel = await connection.channel()

	# Maximum message count which will be processing at the same time.
	# await channel.set_qos(prefetch_count=100)

	# Declaring queue
	queue = await channel.declare_queue("ai_dispose", auto_delete=True)
	# queue 设置 auto_delete=True 会导致服务关闭后 queue 消失，按需保留
	# queue = await channel.declare_queue("ai_dispose", auto_delete=False)

	await queue.consume(process_message)

	try:
		# Wait until terminate
		await asyncio.Future()
	finally:
		await connection.close()


if __name__ == "__main__":
	asyncio.run(main())

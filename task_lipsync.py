import json
import subprocess
import sys
import threading


def lipsync(task_id, source_url, target_url):
	from app import redis_client

	task_dict = json.loads(redis_client.get(task_id))
	task_dict["status"]["code"] = "5"
	task_dict["status"]["statu"] = "pending"
	try:
		redis_client.set(task_id, json.dumps(task_dict, ensure_ascii=False))
	except Exception as e:
		print({
			"code": 500,
			"status": f"task 3 status update failed reason is {e}",
		})

	output_url = f"./allData/output/{task_id}/lipsync.mp4"
	cmd = [
		"python", "facefusion.py", "headless-run",
		"-s", source_url,
		"-t", target_url,
		"-o", output_url,
		"--processors", "face_enhancer", "lip_syncer",
		"--execution-providers", "cuda"
	]
	print("cmd：", cmd)
	# 启动进程并阻塞等待
	# 启动进程并实时捕获输出
	process = subprocess.Popen(
		cmd,
		stdout=subprocess.PIPE,
		stderr=subprocess.STDOUT,  # 合并标准错误到标准输出
		text=True,  # 以文本模式读取输出
		bufsize=1,  # 行缓冲模式
		universal_newlines=True,  # 兼容不同系统的换行符
		encoding='utf-8',  # 显式使用 utf-8 编码
		errors='replace',  # ✅ 防止乱码引发崩溃
	)

	try:
		output_lines = []

		def print_output():
			for line in process.stdout:
				sys.stdout.write(line)
				sys.stdout.flush()
				output_lines.append(line)
			process.stdout.close()  # ✅ 添加这行，确保读完后关闭流

		# 启动输出打印线程
		output_thread = threading.Thread(target=print_output)
		output_thread.start()

		process.wait()  # 阻塞直到命令完成
		print(f"🔍 子进程返回码: {process.returncode}")
		output_thread.join()  # 确保输出线程结束
	except Exception as e:
		print("出现报错--->>>", e)
	if process.returncode == 0:
		print("进入到---》》》结束处理")

		task_dict["status"]["code"] = "5"
		task_dict["status"]["statu"] = "success"
		# task_dict["lipsync"] = f"http://{app.config.IP}/ai/get/lipsync?taskId={task_id}"
		task_dict["lipsync"] = f"http://127.0.0.1/ai/get/lipsync?taskId={task_id}"
		print("task_dict--->>>", task_dict)
		try:
			redis_client.set(task_id, json.dumps(task_dict, ensure_ascii=False))
		except Exception as e:
			print({
				"code": 500,
				"status": f"task 3 status update failed reason is {e}",
			})
		print("✅ FaceFusion 处理完成！")
	else:
		task_dict["status"]["code"] = "5"
		task_dict["status"]["statu"] = "fail"
		try:
			redis_client.set(task_id, json.dumps(task_dict, ensure_ascii=False))
		except Exception as e:
			print({
				"code": 500,
				"status": f"task 3 status update failed reason is {e}",
			})

		# 获取完整的输出内容
		error_message = "".join(output_lines)
		print(f"❌ 处理失败，错误信息:\n{error_message}")


if __name__ == '__main__':
	lipsync("ID17493502824517066", "./allData/output/ID17493502824517066/el.wav",
			"./allData/upload/ID17493502824517066/ID17493502824517066.mp4")

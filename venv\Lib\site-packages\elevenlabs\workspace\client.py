# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ..core.client_wrapper import <PERSON>ync<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ..core.request_options import RequestOptions
from ..types.default_sharing_preferences_response_model import DefaultSharingPreferencesResponseModel
from ..types.share_option_response_model import Share<PERSON><PERSON>ResponseModel
from .groups.client import Async<PERSON>roups<PERSON><PERSON>, GroupsClient
from .invites.client import AsyncInvites<PERSON><PERSON>, InvitesClient
from .members.client import AsyncMembersClient, MembersClient
from .raw_client import AsyncRawWorkspaceClient, RawWorkspaceClient
from .resources.client import AsyncResourcesClient, ResourcesClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class WorkspaceClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawWorkspaceClient(client_wrapper=client_wrapper)
        self.groups = GroupsClient(client_wrapper=client_wrapper)

        self.invites = InvitesClient(client_wrapper=client_wrapper)

        self.members = MembersClient(client_wrapper=client_wrapper)

        self.resources = ResourcesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawWorkspaceClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawWorkspaceClient
        """
        return self._raw_client

    def update_user_auto_provisioning(
        self, *, enabled: bool, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Update user auto provisioning settings for the workspace.

        Parameters
        ----------
        enabled : bool

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.update_user_auto_provisioning(
            enabled=True,
        )
        """
        _response = self._raw_client.update_user_auto_provisioning(enabled=enabled, request_options=request_options)
        return _response.data

    def get_default_sharing_preferences(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DefaultSharingPreferencesResponseModel:
        """
        Get the user's default sharing preferences.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DefaultSharingPreferencesResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.get_default_sharing_preferences()
        """
        _response = self._raw_client.get_default_sharing_preferences(request_options=request_options)
        return _response.data

    def update_default_sharing_preferences(
        self, *, default_sharing_groups: typing.Sequence[str], request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Update the user's default sharing preferences.

        Parameters
        ----------
        default_sharing_groups : typing.Sequence[str]
            List of group IDs to share with by default

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.update_default_sharing_preferences(
            default_sharing_groups=["default_sharing_groups"],
        )
        """
        _response = self._raw_client.update_default_sharing_preferences(
            default_sharing_groups=default_sharing_groups, request_options=request_options
        )
        return _response.data

    def get_share_options(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.List[ShareOptionResponseModel]:
        """
        Get the share options for a workspace.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[ShareOptionResponseModel]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.get_share_options()
        """
        _response = self._raw_client.get_share_options(request_options=request_options)
        return _response.data


class AsyncWorkspaceClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawWorkspaceClient(client_wrapper=client_wrapper)
        self.groups = AsyncGroupsClient(client_wrapper=client_wrapper)

        self.invites = AsyncInvitesClient(client_wrapper=client_wrapper)

        self.members = AsyncMembersClient(client_wrapper=client_wrapper)

        self.resources = AsyncResourcesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawWorkspaceClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawWorkspaceClient
        """
        return self._raw_client

    async def update_user_auto_provisioning(
        self, *, enabled: bool, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Update user auto provisioning settings for the workspace.

        Parameters
        ----------
        enabled : bool

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.update_user_auto_provisioning(
                enabled=True,
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update_user_auto_provisioning(
            enabled=enabled, request_options=request_options
        )
        return _response.data

    async def get_default_sharing_preferences(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DefaultSharingPreferencesResponseModel:
        """
        Get the user's default sharing preferences.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DefaultSharingPreferencesResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.get_default_sharing_preferences()


        asyncio.run(main())
        """
        _response = await self._raw_client.get_default_sharing_preferences(request_options=request_options)
        return _response.data

    async def update_default_sharing_preferences(
        self, *, default_sharing_groups: typing.Sequence[str], request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Update the user's default sharing preferences.

        Parameters
        ----------
        default_sharing_groups : typing.Sequence[str]
            List of group IDs to share with by default

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.update_default_sharing_preferences(
                default_sharing_groups=["default_sharing_groups"],
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update_default_sharing_preferences(
            default_sharing_groups=default_sharing_groups, request_options=request_options
        )
        return _response.data

    async def get_share_options(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.List[ShareOptionResponseModel]:
        """
        Get the share options for a workspace.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[ShareOptionResponseModel]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.get_share_options()


        asyncio.run(main())
        """
        _response = await self._raw_client.get_share_options(request_options=request_options)
        return _response.data

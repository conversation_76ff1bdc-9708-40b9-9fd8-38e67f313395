from celery import Celery

celery = Celery()


def init_celery(app):
	celery.conf.update(
		broker_url=app.config["CELERY_BROKER_URL"],
		result_backend=app.config["CELERY_RESULT_BACKEND"]
	)
	celery.Task = create_context_task(app)  # 解决上下文问题
	return celery


def create_context_task(app):
	class ContextTask(celery.Task):
		def __call__(self, *args, **kwargs):
			with app.app_context():  # 确保任务运行时拥有上下文
				return self.run(*args, **kwargs)

	return ContextTask

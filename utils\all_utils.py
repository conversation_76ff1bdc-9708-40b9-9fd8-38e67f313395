import os.path

import redis
from moviepy.audio.io.AudioFileClip import Audio<PERSON>ileClip

from app import app


def set_redis(key, data, db, timeout=None):
    redis_client = redis.Redis(host=app.config["IP"], port=6379, db=db)
    response = redis_client.set(key, data)
    if timeout:
        redis_client.expire(key, timeout)
    return response


def get_redis(key, db):
    redis_client = redis.Redis(host=app.config["IP"], port=6379, db=db)
    # redis_client = redis.Redis(host=current_ip, port=6379, db=db, password="123456")

    return redis_client.get(key)


async def check_text_to_speech_time():
    pass


async def audio_cutting(task_id, start_sec, end_sec, number):
    path = f"./allData/output/{task_id}"
    music = AudioFileClip(f"{path}/separation.wav")
    out_music = music.subclip(start_sec, end_sec)
    save_path = f"{path}/cutting"
    if not os.path.exists(save_path):
        os.mkdir(save_path)
    out_music.write_audiofile(f"{save_path}/{number}.mp3")

    print(f"audio cutting {number} success. save path to {save_path}/{number}.mp3")


if __name__ == '__main__':
    # -----------设置redis-----------
    b = set_redis('key', 'data')
    print(b)

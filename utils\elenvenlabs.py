import asyncio
import math
import os
import time

import aiofiles
import aiohttp

import requests
from elevenlabs.client import ElevenLabs
from moviepy.audio.AudioClip import concatenate_audioclips
from moviepy.audio.io.AudioFileClip import AudioFileClip

from config import Config

client = ElevenLabs(api_key=Config.elevenlabs_key)


def audio_clip(taskId):
	init_audio_path = f"./allData/output/{taskId}"
	# ---- 执行切割 ----
	audio = AudioFileClip(os.path.join(init_audio_path, "narakeet.wav"))
	try:
		total_time = audio.duration
		if total_time > 250:
			# 1、校验clip文件夹是否存在 -- 不存在则创建
			if not os.path.exists(os.path.join(init_audio_path, "clip")):
				os.makedirs(os.path.join(init_audio_path, "clip"))

			# 2、计算需要切割的次数
			number = math.ceil(total_time / 250)

			# 3、进入循环对音频进行切割
			for i in range(number):
				print("进入切割")
				start_time = i * 250
				end_time = min((i + 1) * 250, total_time)
				clip_audio = audio.subclipped(start_time, end_time)
				clip_audio.write_audiofile(os.path.join(init_audio_path, "clip", f"{i}.wav"))
	finally:
		# 确保音频被关闭
		audio.close()


def CloneVideo(taskId):
	print("进入获取克隆id")
	audio_path = f"./allData/output/{taskId}/separation.wav"
	audio = AudioFileClip(audio_path)
	if audio.duration > 60:
		clip_audio = audio.subclipped(0, 60)
		audio_path = f"./allData/output/{taskId}/clip_audio.wav"
		clip_audio.write_audiofile(audio_path)

	voice_id = ""
	while True:
		try:
			client = ElevenLabs(api_key=Config.elevenlabs_key)
			print("执行的path--->>>", audio_path)
			voice = client.clone(
				name=taskId,
				description="",
				files=[audio_path]
			)
			print("API回复：", voice)
			voice_id = voice.voice_id
			print("voice_id：", voice_id)
			if voice_id:
				break
		except Exception as e:
			print("voice id get failed reason is ", e)
			time.sleep(5)
			continue

	return voice_id


async def generate_audio(voice_id, audio_file, save_path=None):
	url = f"https://api.elevenlabs.io/v1/speech-to-speech/{voice_id}/stream?output_format=mp3_44100_128"
	headers = {
		"Xi-Api-Key": "***************************************************"
	}

	# 打开文件并读取内容
	async with aiofiles.open(audio_file, 'rb') as f:
		file_data = await f.read()  # 异步读取文件内容
	# 打开文件进行上传
	data = aiohttp.FormData()
	data.add_field('model_id', 'eleven_multilingual_sts_v2')
	data.add_field('audio', file_data, content_type="audio/wav")

	retry_number = 3
	while retry_number > 0:
		async with aiohttp.ClientSession() as session:
			async with session.post(url, headers=headers, data=data) as response:
				# 使用 aiofiles 异步写入文件
				if response.status == 200:
					# 使用 aiofiles 异步写入文件
					print("clone success save path is : ", save_path if save_path else audio_file)
					async with aiofiles.open(save_path if save_path else audio_file, "wb") as f:
						async for chunk in response.content.iter_chunked(1024):  # 每次读取1024字节
							if isinstance(chunk, bytes):
								await f.write(chunk)  # 异步写入音频数据

					print(f"文件已保存为 {save_path if save_path else audio_file}")
					break
				else:
					retry_number -= 1
					print(f"请求失败: {response.status}, {await response.text()}")
					time.sleep(5)
					continue


async def create_el_run(task_id, voice_id):
	# # 2、执行克隆
	clip_file_path = f"./allData/output/{task_id}/clip/"
	if os.path.exists(clip_file_path):
		print("进入if 克隆")
		all_audio_file = os.listdir(clip_file_path)
		print(all_audio_file)
		all_task_list = []
		for one_audio in all_audio_file:
			clip_audio_path = os.path.join(clip_file_path, one_audio)
			all_task_list.append(asyncio.create_task(generate_audio(voice_id, clip_audio_path)))

		# 执行克隆
		await asyncio.wait(all_task_list)

		# 合并音频
		all_audio_file.sort()
		print("排序过后的clone列表：", all_audio_file)
		all_audio_list = []
		for one_audio in all_audio_file:
			clip_audio_path = os.path.join(clip_file_path, one_audio)
			all_audio_list.append(AudioFileClip(clip_audio_path))

		final_clip = concatenate_audioclips(all_audio_list)
		final_clip.write_audiofile(f"./allData/output/{task_id}/el.wav")
		# 关闭所有剪辑
		for clip in all_audio_list:
			clip.close()
		print(f"音频合并完成，已保存到: ./allData/output/{task_id}/el.wav")
	else:
		# 执行克隆
		print("进入else 克隆")
		await generate_audio(voice_id, audio_file=f"./allData/output/{task_id}/narakeet.wav",
							 save_path=f"./allData/output/{task_id}/el.wav")

	return 200


def audio_Isolation(file_name):
	try:
		with open(f"./InputAudio/{file_name}", "rb") as audio_file:
			# Perform audio isolation
			url = "https://api.elevenlabs.io/v1/audio-isolation"
			files = {"audio": audio_file}
			headers = {"xi-api-key": Config.elevenlabs_key}
			response = requests.post(url, files=files, headers=headers)

			# Save the isolated audio to a new file
			newFileName = file_name.split('.')[0]
			output_file_path = f"./ClipVideo/{newFileName}/"
			if not os.path.exists(output_file_path):
				os.mkdir(output_file_path)
			with open(f"{output_file_path}{newFileName}.wav", "wb") as output_file:
				for chunk in response:
					output_file.write(chunk)
		print(f"Isolated audio saved to {output_file_path}")
		response = {
			"statusCode": 200,
			"error": ""
		}

	except Exception as e:
		response = {
			"statusCode": 500,
			"error": str(e)
		}
	return response


if __name__ == '__main__':
	# print(os.path.exists(f'../allData/upload/{1}/{1}.mp3'))
	audio_clip("ID17482389981188984")
# asyncio.run(create_el_run("ID1747206922602345"))

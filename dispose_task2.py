import asyncio
import json
import logging
from concurrent.futures import ThreadPoolExecutor

import pika

from utils.task_generate_clone_audio import generate_clone_audio
from utils.task_init_dispose_video import dispose_video
from task_lipsync import lipsync
from utils.translate import process_vtt  # 这个函数需要是同步版本或使用异步适配器

logging.basicConfig(level=logging.INFO)

executor = ThreadPoolExecutor(max_workers=4)


def handle_task(body):
	task_id = body.get("taskId")
	dispose_type = body.get("dispose_type")
	target_languages = body.get("language")
	is_enabled = body.get("is_enabled")

	try:
		if dispose_type == "dispose_video":
			print("正在执行 --- dispose_video ---- ")
			original_lang = body.get("original_lang")
			task_dict = body.get("task_dict")
			dispose_video(task_id, original_lang, task_dict)

			print(f"🚀 开始翻译处理 task: {task_id}")
			run_state = asyncio.run(process_vtt(task_id, target_languages))
			print("翻译处理完成，状态：", run_state)

			if is_enabled == "on" and run_state == "success":
				print(f"开始语音克隆 task: {task_id}")
				asyncio.run(generate_clone_audio(task_id, target_languages))

		elif dispose_type == "ai_translate":
			print("正在执行 --- ai_translate ---- ")
			asyncio.run(process_vtt(task_id, target_languages))

		elif dispose_type == "generate_clone":
			print("正在执行 --- generate_clone ---- ")
			asyncio.run(generate_clone_audio(task_id, target_languages))

		elif dispose_type == "run_lipsync":
			print("正在执行 --- run_lipsync ---- ")
			source_url = body.get("source_url")
			target_url = body.get("target_url")
			lipsync(task_id, source_url, target_url)

	except Exception as e:
		logging.exception(f"❌ 后台任务执行失败：{e}")


def process_message(ch, method, properties, body):
	try:
		body = json.loads(body)
		print("🎯 接收到任务:", body.get("dispose_type"))

		# 提交任务到后台线程池
		executor.submit(handle_task, body)

		# 立即 ack，不等任务执行完，避免卡住主线程
		if ch.is_open:
			ch.basic_ack(delivery_tag=method.delivery_tag)
	except Exception as e:
		logging.exception("❌ 消息处理失败：%s", e)
		try:
			if ch.is_open and ch.connection and ch.connection.is_open:
				ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
		except Exception as nack_err:
			logging.exception("⚠️ nack 出错: %s", nack_err)


# def process_message(ch, method, properties, body):
# 	try:
# 		body = json.loads(body)
# 		dispose_type = body.get("dispose_type")
# 		print("获取到的dispose_type:", dispose_type)
# 		task_id = body.get("taskId")
# 		target_languages = body.get("language")
#
# 		if dispose_type == "dispose_video":
# 			print("正在执行 --- dispose_video ---- ")
# 			original_lang = body.get("original_lang")
# 			task_dict = body.get("task_dict")
# 			is_enabled = body.get("is_enabled")
# 			dispose_video(task_id, original_lang, task_dict)
# 			print("开始执行任务三~~~")
#
# 			try:
# 				print(f"🚀 开始处理 task: {task_id}")
# 				# run_state = asyncio.run(process_vtt(task_id, target_languages))  # 假设这是同步函数
# 				run_state = run_async_task(process_vtt(task_id, target_languages))
# 				print(f"✅ task: {task_id} 成功处理")
# 				print("执行翻译返回的内容：", run_state)
# 			except Exception as e:
# 				logging.exception("❌ 任务处理异常：%s", e)
# 				ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
# 				return
#
# 			if is_enabled == "on" and run_state == "success":
# 				try:
# 					# asyncio.run(generate_clone_audio(task_id, target_languages))
# 					run_async_task(generate_clone_audio(task_id, target_languages))
# 				except Exception as e:
# 					print("task 4 dispose status failed reason is {}".format(e))
#
# 		elif dispose_type == "ai_translate":
# 			print("正在执行 --- ai_translate ---- ")
# 			# asyncio.run(process_vtt(task_id, target_languages))
# 			run_async_task(process_vtt(task_id, target_languages))
#
# 		elif dispose_type == "generate_clone":
# 			print("正在执行 --- generate_clone ---- ")
# 			# asyncio.run(generate_clone_audio(task_id, target_languages))
# 			run_async_task(generate_clone_audio(task_id, target_languages))
#
# 		elif dispose_type == "run_lipsync":
# 			print("正在执行 --- run_lipsync ---- ")
# 			source_url = body.get("source_url")
# 			target_url = body.get("target_url")
# 			lipsync(task_id, source_url, target_url)
#
# 		# ack 和 nack 必须单独 try 包裹
# 		try:
# 			if ch.is_open:
# 				ch.basic_ack(delivery_tag=method.delivery_tag)
# 			else:
# 				print("❗ ack失败：channel 已关闭")
# 		except Exception as e:
# 			logging.exception("⚠️ ack 出错: %s", e)
#
# 	except Exception as e:
# 		logging.exception("❌ 消息处理失败：%s", e)
#
# 		try:
# 			if ch.is_open and ch.connection and ch.connection.is_open:
# 				ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
# 			else:
# 				print("❗ nack失败：channel 或 connection 已关闭")
# 		except Exception as nack_err:
# 			logging.exception("⚠️ nack 出错: %s", nack_err)


def main():
	connection = pika.BlockingConnection(
		pika.ConnectionParameters(host="127.0.0.1")
	)
	channel = connection.channel()

	queue_name = "ai_dispose"
	channel.queue_declare(queue=queue_name, durable=True)

	print(" [*] 等待消息。按 CTRL+C 退出")

	channel.basic_qos(prefetch_count=1)
	channel.basic_consume(queue=queue_name, on_message_callback=process_message)
	try:
		channel.start_consuming()
	except KeyboardInterrupt:
		channel.stop_consuming()
		connection.close()


if __name__ == "__main__":
	main()

import os
import sys

from moviepy.video.io.VideoFileClip import VideoFile<PERSON><PERSON>


def check(taskId):
	defaultPath = f'./allData/upload/{taskId}'
	if not os.path.exists(f'{defaultPath}/{taskId}.wav') or os.path.exists(f'{defaultPath}/{taskId}.mp3'):
		if os.path.exists(f'{defaultPath}/{taskId}.mp4'):
			video = VideoFileClip(f'{defaultPath}/{taskId}.mp4')
			video.audio.write_audiofile(f"{defaultPath}/{taskId}.wav")
		else:
			return 404

	return 200

import subprocess
import sys
import threading


def run_facefusion():
	cmd = ['python', 'facefusion.py', 'headless-run', '-s', './allData/output/ID17491085204369199/el.wav', '-t',
		   './allData/upload/ID17491085204369199/ID17491085204369199.mp4', '-o',
		   './allData/output/ID17491085204369199/lipsync.mp4', '--processors', 'face_enhancer', 'lip_syncer',
		   '--execution-providers', 'cuda']
	# 启动进程并阻塞等待
	# 启动进程并实时捕获输出
	process = subprocess.Popen(
		cmd,
		stdout=subprocess.PIPE,
		stderr=subprocess.STDOUT,  # 合并标准错误到标准输出
		text=True,  # 以文本模式读取输出
		bufsize=1,  # 行缓冲模式
		universal_newlines=True,  # 兼容不同系统的换行符
		encoding='utf-8'  # ✅ 显式使用 utf-8 编码

	)

	# 实时打印输出流
	def print_output():
		output_lines = []
		for line in process.stdout:
			sys.stdout.write(line)  # 实时输出日志
			sys.stdout.flush()  # 确保立即显示
			output_lines.append(line)  # 保存日志以便后续处理
		return output_lines

	# 启动输出打印线程
	output_thread = threading.Thread(target=print_output)
	output_thread.start()

	process.wait()  # 阻塞直到命令完成
	output_thread.join()  # 确保输出线程结束
	if process.returncode == 0:
		print("✅ FaceFusion 处理完成！")
	else:
		# 获取完整的输出内容
		output_lines = print_output()
		error_message = "".join(output_lines)
		print(f"❌ 处理失败，错误信息:\n{error_message}")


if __name__ == "__main__":
	run_facefusion()
# run_facefusion.delay()
"python run.py -o D:\Temp\out\A --skip-download --execution-providers cuda --frame-processors face_swapper face_enhancer --face-enhancer-model gpen_bfr_256 --face-enhancer-blend 90 --output-video-encoder hevc_nvenc --output-video-quality 99 --face-mask-types box occlusion"

"""
python ./facefusion/facefusion.py headless-run -s /Users/<USER>/Desktop/testVideo/lip_sync/1.wav -t /Users/<USER>/Desktop/testVideo/lip_sync/test1.mp4 -o ./lipsync.mp4 --processors face_enhancer lip_syncer --execution-providers cpu
"""

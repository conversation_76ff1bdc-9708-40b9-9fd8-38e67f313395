# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .forced_alignment_character_response_model import ForcedAlignmentCharacterResponseModel
from .forced_alignment_word_response_model import ForcedAlignmentWordResponseModel


class ForcedAlignmentResponseModel(UncheckedBaseModel):
    """
    Model representing the response from the aligner service.
    """

    characters: typing.List[ForcedAlignmentCharacterResponseModel] = pydantic.Field()
    """
    List of characters with their timing information.
    """

    words: typing.List[ForcedAlignmentWordResponseModel] = pydantic.Field()
    """
    List of words with their timing information.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow

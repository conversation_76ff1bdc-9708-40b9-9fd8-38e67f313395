# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .add_pronunciation_dictionary_response_model_permission_on_resource import (
    AddPronunciationDictionaryResponseModelPermissionOnResource,
)


class AddPronunciationDictionaryResponseModel(UncheckedBaseModel):
    id: str = pydantic.Field()
    """
    The ID of the created pronunciation dictionary.
    """

    name: str = pydantic.Field()
    """
    The name of the created pronunciation dictionary.
    """

    created_by: str = pydantic.Field()
    """
    The user ID of the creator of the pronunciation dictionary.
    """

    creation_time_unix: int = pydantic.Field()
    """
    The creation time of the pronunciation dictionary in Unix timestamp.
    """

    version_id: str = pydantic.Field()
    """
    The ID of the created pronunciation dictionary version.
    """

    version_rules_num: int = pydantic.Field()
    """
    The number of rules in the version of the pronunciation dictionary.
    """

    description: typing.Optional[str] = pydantic.Field(default=None)
    """
    The description of the pronunciation dictionary.
    """

    permission_on_resource: typing.Optional[AddPronunciationDictionaryResponseModelPermissionOnResource] = (
        pydantic.Field(default=None)
    )
    """
    The permission on the resource of the pronunciation dictionary.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow

import asyncio
import logging
import os
import librosa
import soundfile as sf

from openai import AsyncOpenAI
from pydub import AudioSegment
from webvtt import WebVTT

from config import Config
from utils.asyncNarakeet import narakeetRun

# 日志信息配置
cur_dir = os.path.abspath(__file__).rsplit("\\", 1)[0]
log_path = os.path.join(cur_dir, "logs", "ai_translate.log")
logging.basicConfig(filename=log_path, level=logging.DEBUG,
					filemode='w', format='%(levelname)s:%(asctime)s:%(message)s', datefmt='%Y-%d-%m %H:%M:%S')

# 参数
TOLERANCE = 1.0  # 允许偏差秒数


async def translate_text(text, lang, target_duration):
	print("进入翻译函数")
	prompt = (
		f"请将以下内容翻译成{lang}，翻译后内容应适合在{target_duration:.1f}秒内自然朗读，"
		f"不要太短也不要太长，语句自然流畅。\n原文：{text}"
	)
	print("prompt：", prompt)
	try:
		async with AsyncOpenAI(api_key=Config.openAI_key) as client:
			completion = await asyncio.wait_for(
				client.chat.completions.create(
					model="gpt-4o",
					messages=[{"role": "user", "content": prompt}],
					temperature=0.7
				),
				timeout=30  # 显式管理整体请求超时
			)
			return completion.choices[0].message.content
	except asyncio.TimeoutError:
		logging.error(f"[翻译超时] === 内容：{text}")
	except asyncio.CancelledError:
		logging.error(f"[翻译任务被取消] === 内容：{text}")
	except Exception as e:
		logging.error(f"[翻译失败] === 内容：{text} === 原因：{e}")
	return text  # fallback


# ⏱ 字幕时间格式转秒
def time_to_seconds(t):
	h, m, s = t.split(":")
	s, ms = s.split(".")
	return int(h) * 3600 + int(m) * 60 + int(s) + int(ms) / 1000.0


def time_to_ms(timestamp):
	"""00:00:10.500 -> 毫秒"""
	h, m, s = timestamp.split(":")
	s, ms = s.split(".")
	return (int(h) * 3600 + int(m) * 60 + int(s)) * 1000 + int(ms.ljust(3, '0'))


async def generate_audio(number, task_id, text, duration, target_lang, start_time, end_time):
	print(f"正在处理{number}任务")
	init_path = f"./allData/output/{task_id}"
	retry_count = 1
	try:
		while True:
			logging.info(f"第{number}句 原内容：{text} 时间：({duration:.2f}s) ")
			translated = await translate_text(text, lang=target_lang, target_duration=duration)
			logging.info(f"第{number}句 翻译：{translated}")
			print(f"第{number}句 翻译：{translated}")

			# 合成语音并检查时长
			logging.info(
				f"推入narakeet信息 --- type:text data:{translated} taskID:{task_id} language:{target_lang} number:{number}")
			await narakeetRun(type="text", data=translated, taskID=task_id, language=target_lang, number=number)

			# 检查长度
			y, sr = librosa.load(os.path.join(init_path, f"{number}.wav"), sr=None)
			actual_duration = librosa.get_duration(y=y, sr=sr)
			logging.info(
				f"第{number}句 === 翻译：{translated} === 原始时间：{duration} === 翻译后的时间：{actual_duration} === 重试次数：{retry_count}")
			print(
				f"第{number}句 === 翻译：{translated} === 原始时间：{duration} === 翻译后的时间：{actual_duration} === 重试次数：{retry_count}")
			if abs(actual_duration - duration) <= TOLERANCE or retry_count > 1:
				logging.info(
					f"第{number}句 === 翻译：{translated} === 原始时间：{duration} === 翻译后的时间：{actual_duration} === 结束")
				break
			retry_count += 1
			print("正在执行重试--->>>", retry_count)

		# 加载并拉伸音频（如有需要）
		y, sr = librosa.load(os.path.join(init_path, f"{number}.wav"), sr=None)
		current_duration = librosa.get_duration(y=y, sr=sr)
		rate = current_duration / duration
		if 0.9 < rate < 1.1:  # 只在自然范围内调整
			logging.info(f"拉伸比率：{rate:.2f}")
			y_fixed = librosa.effects.time_stretch(y, rate=rate)
		else:
			y_fixed = y

		output_path = os.path.join(init_path, f"line_{number:03}.wav")
		sf.write(output_path, y_fixed, sr)
		logging.info(f"✅ 输出：{output_path}")
		print(f"✅ 输出：{output_path}")
	except Exception as e:
		print(f"第 {number} 句失败：{e}")
		return e
	print(f"完成第 {number} 句")
	return {"start": str(start_time), "end": str(end_time), "text": translated,
			"number": number}


# 📍 主处理函数
async def process_vtt(task_id, target_lang):
	logging.info(f" ==== 开始执行 {task_id} 任务翻译 ====")
	init_path = f"./allData/output/{task_id}"
	vtt_path = os.path.join(init_path, "tran.vtt")
	vtt = WebVTT().read(vtt_path)

	all_task_list = []
	for i, caption in enumerate(vtt):
		start = caption.start
		end = caption.end
		text = caption.text.strip().replace("\n", " ")
		start_sec = time_to_seconds(start)
		end_sec = time_to_seconds(end)
		duration = end_sec - start_sec
		all_task_list.append(generate_audio(i + 1, task_id, text, duration, target_lang, start, end))

	all_save_vtt_texts = await asyncio.gather(*all_task_list, return_exceptions=True)
	print("翻译处理完毕---》》》", all_save_vtt_texts)

	sorted_data = sorted(all_save_vtt_texts, key=lambda x: x['number'])

	print("sorted_data：", sorted_data)

	with open(os.path.join(init_path, "translate.vtt"), "w", encoding="utf-8") as file:
		# 写入文件头
		file.write("WEBVTT\n\n")

		for subtitle in sorted_data:
			# 写入序号
			file.write(f"{subtitle["number"]}\n")
			# 写入时间戳
			file.write(f"{subtitle['start']} --> {subtitle['end']}\n")
			# 写入字幕内容
			file.write(f"{subtitle['text']}\n\n")

	vtt = WebVTT().read(vtt_path)
	output_audio = AudioSegment.silent(duration=0)
	current_time = 0  # 当前合成位置（毫秒）

	for i, caption in enumerate(vtt):
		start_ms = time_to_ms(caption.start)
		end_ms = time_to_ms(caption.end)
		duration = end_ms - start_ms

		# 当前片段的音频
		audio_path = os.path.join(init_path, f"line_{i + 1:03}.wav")
		if not os.path.exists(audio_path):
			print(f"❌ 缺失音频：{audio_path}")
			continue

		speech = AudioSegment.from_wav(audio_path)

		# 若开始时间 > 当前音频长度，补静音
		if start_ms > current_time:
			gap = start_ms - current_time
			output_audio += AudioSegment.silent(duration=gap)
			current_time += gap

		# 添加音频
		output_audio += speech
		current_time += len(speech)

		# 若音频不足该段原始时长，补静音
		if current_time < end_ms:
			pad = end_ms - current_time
			output_audio += AudioSegment.silent(duration=pad)
			current_time += pad

	output_audio.export(os.path.join(init_path, "narakeet.wav"), format="wav")
	print(f"✅ 合成完成：{os.path.join(init_path, "narakeet.wav")}")


# ✅ 运行
if __name__ == "__main__":
	asyncio.run(process_vtt("ID17482265417065238", "中文"))

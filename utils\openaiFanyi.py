import asyncio

from openai import AsyncOpenAI

from config import Config

lanNames = {
    "中文": 'Simplified Chinese',
    "英文": 'English',
    "高棉語": 'Khmer',
    "粵語": 'Cantonese',
    "越南語": "Vietnamese",
    "日語": "Japanese",
    "印尼語": "Indonesian",
    "泰語": "Thai",
    "阿拉伯文": "Arabic",
    "法文": "French",
    "西班牙文": "Spanish",
    "马来文": "Malay",
    "韩语": 'Korean',
}


async def openaiRun(taskId, language):
    prompt = f"""
                Translate the following sentence into {lanNames[language]}, keeping the translated sentence similar in length and timing to the original speech. Prioritize short, concise phrasing if needed to fit the original speech duration.

                INPUT VTT:
                {open(f"./allData/output/{taskId}/tran.vtt", "r").read()}
            """
    try:
        async with AsyncOpenAI(api_key=Config.openAI_key) as client:
            completion = await client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": f"You are a translation robot proficient in various languages"},
                    {"role": "user", "content": prompt}
                ]
            )
            translation = completion.choices[0].message.content.replace("```vtt", "").replace("```", "").replace("\"",
                                                                                                                 "").replace(
                "\nWEBVTT", "WEBVTT")
            with open(f"./allData/output/{taskId}/translate.vtt", "w") as f:
                f.write(translation)

        return 200
    except Exception as e:
        print("错误：", e)
        return e


async def translate_text_openai(text, language):
    prompt = f"""
                Translate the following sentence into {lanNames[language]}, keeping the translated sentence similar in length and timing to the original speech. Prioritize short, concise phrasing if needed to fit the original speech duration.

                INPUT TEXT:
                {text}
            """
    try:
        async with AsyncOpenAI(api_key=Config.openAI_key) as client:
            completion = await client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": f"You are a translation robot proficient in various languages"},
                    {"role": "user", "content": prompt}
                ]
            )
            translation = completion.choices[0].message.content
        return translation
    except Exception as e:
        print("错误：", e)
        return e


async def retry_translate_text_openai(original_text, current_time, original_duration, state):
    prompt = f"请{state}下面句子的字数、使得当前句子speech后的时长为{original_duration}秒 当前句子时长：{current_time}秒 注意：你只能按要求进行处理并返回内容即可、不需要其他任何说明：\n{original_text}"
    print("openai -- prompt：", prompt)
    try:
        async with AsyncOpenAI(api_key=Config.openAI_key) as client:
            completion = await client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            translation = completion.choices[0].message.content
        return translation
    except Exception as e:
        print("openai -- fail：", e)
        return e


if __name__ == '__main__':
    # asyncio.run(openaiRun("1", "英文"))
    asyncio.run(translate_text_openai("你好呀", "英文"))

    """
    Requirements: Please translate the following VTT file data into {lanNames[language]}
            Note: Please return according to the format passed in
            Notice: Certainly! Below is the translated content in Chinese (zh) while maintaining the original VTT format: is not allowed. Please return to the content directly.
            vtt：{open(f"./allData/output/{taskId}/tran.vtt", "r").read()}
            Output Format：""
    """

    """
    Translate the following sentence into [target language], keeping the translated sentence similar in length and timing to the original speech. Prioritize short, concise phrasing if needed to fit the original speech duration.
    """

    """
    Translate the following VTT subtitles into {lanNames[language]} under STRICT constraints:

                1. **MANDATORY RULES**
                   - Preserve ALL timestamps (e.g. `00:00:01.000 --> 00:00:03.000`) exactly.
                   - Translation MUST fit original duration. Adjust text length using:
                     * Shorten: Remove filler words, use abbreviations.
                     * Extend: Add brief adjectives/adverbs if too short.
                   - NEVER exceed original word count by more than 10%.

                2. **TEXT CONTROL**
                   - Split/merge sentences ONLY if timing matches.
                   - Replace long phrases with shorter equivalents (e.g. "in order to" → "to").

                3. **OUTPUT**
                   - Return PURE VTT format. No headers/comments.
                   - If timing cannot match, add `[CHECK_TIMING]` flag.
    """

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Translate</title>
    <link href="./css/quasar/icon.css" rel="stylesheet" />
    <link href="./css/quasar/fonts-css.css" rel="stylesheet" type="text/css" />
    <link
      href="./css/quasar/animate.min.css"
      rel="stylesheet"
      type="text/css"
    />
    <link href="./css/quasar/quasar.min.css" rel="stylesheet" type="text/css" />
    <link href="./css/index.css" rel="stylesheet" type="text/css" />
    <script src="./plugin/vue.min.js"></script>
    <script src="./plugin/jquery-3.2.1.min.js"></script>
  </head>

  <body>
    <div id="el-stop-1" v-cloak>
      <div class="home-container row flex-center">
        <div class="overlay"></div>
        <div class="home-menu">
          <q-btn color="teal" icon="videocam" no-caps @click="onTranslateRoute('video')">
            <div>
              獲取視頻字幕
            </div>
          </q-btn>
          <q-btn color="deep-orange" icon="graphic_eq" no-caps @click="onTranslateRoute('audio')">
            <div>
              文字轉語音
            </div>
          </q-btn>
        </div>
      </div>
    </div>
  </body>
</html>

<script>
  window.quasarConfig = {
    brand: {
      primary: "#014aa1",
      secondary: "#26A69A",
      accent: "#9C27B0",

      dark: "#1d1d1d",

      positive: "#21BA45",
      negative: "#C10015",
      info: "#31CCEC",
      warning: "#F2C037",
    },
    loadingBar: { skipHijack: true },
  };
</script>
<script src="./plugin/quasar.umd.min.js"></script>
<script src="./plugin/quasar.ie.polyfills.umd.min.js"></script>
<script src="./css/quasar/zh-hant.umd.min.js"></script>
<script>
  Quasar.lang.set(Quasar.lang.zhHant);
</script>

<script>
  new Vue({
    el: "#el-stop-1",
    data: function () {
      return {
        langOption: [
          "中文",
          "粵語",
          "英文",
          "越南語",
          "日語",
          "印尼語",
          "高棉語",
          "泰語",
          "阿拉伯文",
          "法文",
          "西班牙文",
          "马来文",
          "韩语",
        ],
        selectedFile: null,
        upFileLoading: false,
        fileId: "",
        videoUrl: null,
        fileName: "",
        fileType: "0",
        langObj: {
          form: "zh",
          to: "中文",
        },
        msg: "",
        msgDataList: [
          // {
          //     id: "39",
          //     data: "谢谢你。",
          //     allTime: "1.00",
          //     voiceTime: "0.60",
          //     videoUrl: "",
          // },
        ],
        upMsgDataList: [], // 視頻原文內容(有修改需要更新的)
        translateMsg: [
          // {
          //     "id": "39",
          //     "data": "谢谢你。",
          //     "allTime": "1.00",
          //     "voiceTime": 3.875,
          //     videoUrl: ''
          // }
        ], // 翻譯後內容
        upTranslateMsg: [], // 翻譯後內容(有修改需要更新的)
        aiReprocessingContentLoading: false, // AI重新處理內容Loading狀態
        getAudio: null,
        getAudioKey: 1,
        getAudioLoading: false,
        audioType: "1",
        VoiceName: null,
        getVoiceNameLoading: false,
        getVoiceList: [],
        pickVoice: "",
        getGenerateCloneLoading: false,
        getGenerateCloneUrl: null,
        stopApiData: {
          checkMesg: {
            loading: false,
            status: false,
          },
        },
        submitTranslateLoading: false,
        getAudioStatusTimeout: null, // 語音生成狀態定時器
        getGenerateCloneStatusTimeout: null, // 語音克隆狀態定時器
        getAudioTimeLoading: false,
        videoLang: "", // 上傳視頻的語言
        // 2024-01-13
        translateType: "",
        audioValue: "",
      };
    },
    methods: {
      // 2025-01-13
      // onTranslateRoute
      onTranslateRoute(type){
        if(type==='video'){
          location.href = location.origin + '/video.html'
        }else {
          location.href = location.origin + '/audio.html'
        }
      },
    },
    mounted() {},
    beforeDestroy() {
      console.log("離開...");
    },
  });
</script>

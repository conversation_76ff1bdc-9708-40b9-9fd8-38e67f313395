import json

from utils.Transkriptor import RunTranskriptor
from utils.audioSeparation import audioSeparation
from utils.check_is_audio import check


def dispose_video(task_id, original_lang, task_dict):
	from app import redis_client
	""""
	{
		"taskId": task_id,
		"image": "",
		"video": "",
		"original_lang": original_lang,
		"original_text": [],
		"translate_lang": translate_lang,
		"translate_text": "",
		"audio": "",
		"status": {
			"code": "1",
			"statu": "pending"}
	}
	"""
	print("task_dict：", task_dict)
	# 1、更新任务进度
	task_dict["status"]["code"] = "2"
	task_dict["status"]["statu"] = "pending"
	try:
		redis_client.set(task_id, json.dumps(task_dict, ensure_ascii=False))
	except Exception as e:
		return {
			"code": 500,
			"status": f"task 2 status update failed reason is {e}",
		}

	# 执行任务2 --- 分离预处理音频的背景音乐
	check_status = check(task_id)
	if check_status != 200:
		task_dict["status"]["statu"] = "fail"
		redis_client.set(task_id, json.dumps(task_dict, ensure_ascii=False))
		return {
			"statusCode": 400,
			"statusMessage": "No audio available",
		}

	separation_state = audioSeparation(task_id)
	if separation_state != 200:
		task_dict["status"]["statu"] = "fail"
		redis_client.set(task_id, json.dumps(task_dict, ensure_ascii=False))
		return {
			"code": 500,
			"status": "audio separation error",
		}

	# 执行任务2 --- 将音频里的内容读取处理、并存档为vtt文件、以及汇总成列表返回
	response_list = RunTranskriptor(task_id, original_lang)
	if response_list == "":
		task_dict["status"]["statu"] = "fail"
		redis_client.set(task_id, json.dumps(task_dict, ensure_ascii=False))
		return {
			"code": 500,
			"status": f"Transkriptor error",
		}

	# 执行任务2 --- 将提取到的内容更新到任务字典里
	task_dict["status"]["statu"] = "success"
	task_dict["original_text"] = response_list
	try:
		redis_client.set(task_id, json.dumps(task_dict, ensure_ascii=False))
	except Exception as e:
		return {
			"code": 500,
			"status": f"task 2 success status update failed reason is {e}",
		}

	return "Success"

# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class CreateTwilioPhoneNumberRequest(UncheckedBaseModel):
    phone_number: str = pydantic.Field()
    """
    Phone number
    """

    label: str = pydantic.Field()
    """
    Label for the phone number
    """

    sid: str = pydantic.Field()
    """
    Twilio Account SID
    """

    token: str = pydantic.Field()
    """
    Twilio Auth Token
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow

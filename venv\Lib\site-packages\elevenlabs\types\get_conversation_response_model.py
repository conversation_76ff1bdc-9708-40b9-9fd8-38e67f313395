# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .conversation_history_analysis_common_model import ConversationHistoryAnalysisCommonModel
from .conversation_history_metadata_common_model import ConversationHistoryMetadataCommonModel
from .conversation_history_transcript_common_model_output import ConversationHistoryTranscriptCommonModelOutput
from .conversation_initiation_client_data_request_output import ConversationInitiationClientDataRequestOutput
from .get_conversation_response_model_status import GetConversationResponseModelStatus


class GetConversationResponseModel(UncheckedBaseModel):
    agent_id: str
    conversation_id: str
    status: GetConversationResponseModelStatus
    transcript: typing.List[ConversationHistoryTranscriptCommonModelOutput]
    metadata: ConversationHistoryMetadataCommonModel
    analysis: typing.Optional[ConversationHistoryAnalysisCommonModel] = None
    conversation_initiation_client_data: typing.Optional[ConversationInitiationClientDataRequestOutput] = None
    has_audio: bool
    has_user_audio: bool
    has_response_audio: bool

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow

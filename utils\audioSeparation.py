import base64
import json
import logging
import math
import os

import requests
import time

from moviepy import AudioF<PERSON>Clip, concatenate_audioclips

from config import Config

from utils.hsToken import hsTokenRun

# Logging setup
logging.basicConfig(level=logging.INFO)

# Constants
domain = "https://sami.bytedance.com"
version = "v4"
namespace = "MusicSourceSeparate"
payload_output_file = "output.json"
is_dump = True


def split_wav(task_id):
	init_path = f"./allData/upload/{task_id}"
	clip = AudioFileClip(os.path.join(init_path, f"{task_id}.wav"))
	duration = clip.duration
	if duration <= 600:
		print("音频小于600秒 无需进行切割")
		return 200

	# 1、计算需要切割的次数
	number = math.ceil(duration / 600)

	# 2、进入循环对音频进行切割
	save_path = []
	for i in range(number):
		print("进入切割")
		start_time = i * 600
		end_time = min((i + 1) * 600, duration)
		clip_audio = clip.subclipped(start_time, end_time)
		save_path.append(os.path.join(init_path, f"{i}.wav"))
		clip_audio.write_audiofile(os.path.join(init_path, f"{i}.wav"))

	return save_path


def upload_audio(audio_path, model):
	# 1、获取身份token
	from utils.all_utils import get_redis
	if not get_redis("hs_token", 1):
		time.sleep(10)
		if not get_redis("hs_token", 1):
			logging.info("initialization hs token")
			token = hsTokenRun()
			print(f"获取到token值：{token}")
	else:
		token = get_redis("hs_token", 1).decode("utf-8")
		print(f"else:获取到token值：{token}")

	# 2、执行音频上传
	try:
		print(f"audio_path:{audio_path}")
		with open(audio_path, "rb") as f:
			content = f.read()
	except FileNotFoundError:
		logging.error("Failed to read file: %s", audio_path)
		return 500
	data = base64.b64encode(content).decode("utf-8")
	body = {
		"data": data,
		"payload": json.dumps({"model": model})
	}
	url_path = f"{domain}/api/v1/invoke?version={version}&token={token}&appkey={Config.hs_app_key}&namespace={namespace}"
	logging.info("Invoke request: %s", url_path)

	# HTTP POST request
	num = 0
	while True:
		try:
			response = requests.post(url_path, json=body, headers={"Content-Type": "application/json"})
			logging.info("Invoke request head: %s", response.headers)
			response.raise_for_status()
			return response
		except requests.RequestException as e:
			logging.error("HTTP request failed: %s", e)
			time.sleep(10)
			if not get_redis("hs_token", 1):
				hsTokenRun()
			print("重试次数：", num)
			num += 1
			continue


def parse_http_response(response, save_path):
	# Parse HTTP response
	try:
		sami_resp = response.json()
	except json.JSONDecodeError:
		logging.error("Failed to parse response: %s", response.text)
		return

	task_id = sami_resp.get("task_id")
	payload = sami_resp.get("payload")
	data = sami_resp.get("data", b"")

	logging.info(
		"Response task_id=%s, payload=%s, data=[%d] bytes",
		task_id, payload, len(data)
	)

	# Dump output
	if is_dump and payload:
		with open(payload_output_file, "w") as f:
			f.write(payload)
	if is_dump and data:
		with open(save_path, "wb") as f:
			f.write(base64.b64decode(data))
	return 200


def merge_audio_clips(clips, output_path):
	final = concatenate_audioclips(clips)
	final.write_audiofile(output_path)
	print(f"合并完成，保存为: {output_path}")


def audioSeparation(taskId, model='2track_vocal'):
	audio_upload_path = f"./allData/upload/{taskId}/{taskId}.wav"

	file_name = "separation-1.wav" if model == "2track_acc" else "separation.wav"

	audio_save_init_path = f"./allData/output/{taskId}"
	os.makedirs(audio_save_init_path, exist_ok=True)

	# 确保上传的音频不要超过十分钟
	clip_response = split_wav(taskId)
	print("clip_response:", clip_response)
	if clip_response == 200:
		response = upload_audio(audio_upload_path, model)
		parse_http_response(response, os.path.join(audio_save_init_path, file_name))
	else:
		print("进入else")
		for path in clip_response:
			response = upload_audio(path, model)
			parse_http_response(response, path)

		# 重新加载已导出的片段进行合并（可避免缓存问题）
		merged_clips = [AudioFileClip(p) for p in clip_response]
		merge_audio_clips(merged_clips, os.path.join(audio_save_init_path, file_name))

	return 200


if __name__ == '__main__':
	# print(audioSeparation("ID17473815424349588", "2track_acc"))
	print(audioSeparation("ID17476585806579708"))
# print(split_wav("ID17473815424349588"))

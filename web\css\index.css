[v-cloak] {
  display: none;
}
.q-video {
  height: 500px;
}

.re-translate {
  position: absolute;
  right: 90px;
  top: 30px;
  z-index: 1;
  opacity: 0.7;
}

.q-inner-loading {
  z-index: 9;
  position: fixed;
}

.q-badge {
  z-index: 1;
}
.home-container {
  position: fixed;
  inset: 0;
  z-index: 10;
}
.overlay {
  position: fixed;
  inset: 0;
  backdrop-filter: blur(4px);
}
.home-menu {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.text-center{
    text-align: center;
}
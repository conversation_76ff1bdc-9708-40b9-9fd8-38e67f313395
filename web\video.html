<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Video Translate</title>
    <link href="./css/quasar/icon.css" rel="stylesheet" />
    <link href="./css/quasar/fonts-css.css" rel="stylesheet" type="text/css" />
    <link
      href="./css/quasar/animate.min.css"
      rel="stylesheet"
      type="text/css"
    />
    <link href="./css/quasar/quasar.min.css" rel="stylesheet" type="text/css" />
    <link href="./css/index.css" rel="stylesheet" type="text/css" />
    <link href="./css/video.css" rel="stylesheet" type="text/css" />
    <script src="./plugin/vue.min.js"></script>
    <script src="./plugin/jquery-3.2.1.min.js"></script>
    <script src="./js/env.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>

    <style>
      .list-btn {
        width: 50px;
        height: 50px;
        right: 60px;
        bottom: 160px !important;
        z-index: 9999;
      }

      .upload-file {
        opacity: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
      }

      #component-container .q-inner-loading {
        width: calc(100% - 280px);
      }

      .delete-btn:hover {
        color: red;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <q-layout view="lhh LpR lff" container style="height: 100vh;">
        <q-page-container>
          <div id="component-container">
            <child-component
              v-if="showComponentId"
              :key="showComponentId"
              :propvalue="{ taskId: showComponentId }">
            </child-component>
            <!-- <child-component
              v-for="(component, index) in multipleFile._files"
              :key="component.taskId"
              v-if="showComponentId === component.taskId"
              ref="childComponentRef"
              :propvalue="component"
              @child-event="data => handleChildEvent(component.taskId, data)"
              @change-img="data => handleChildImg(component.taskId, data)">
            </child-component> -->
          </div>
          <h6 v-if="!showComponentId" class="text-center" style="position: fixed;right: 300px;top: -36px;color: #ccc">
            目前無任務選取，點擊右側任務欄，即可快速瀏覽任務詳情。
            <q-icon name="swipe_right" class="text-gray" style="font-size: 46px;"></q-icon>
          </h6>
        </q-page-container>

        <q-drawer
          side="right"
          v-model="drawerRight"
          bordered
          :width="300"
          :breakpoint="500"
          content-class="bg-grey-1"
          style="padding-bottom: 80px;"
        >
          <q-scroll-area class="fit">
            <div>
              <div class="flex justify-between items-center q-px-sm q-py-xs text-subtitle1 text-white q-mb-sm bg-primary">
                <span>任務列表</span>
                <!-- <q-btn flat round color="white" icon="close"  @click="drawerRight = !drawerRight"></q-btn> -->
              </div>

              <q-list>
                <q-item v-for="(component, index) in multipleFile._files" :key="component.taskId" clickable v-ripple @click="showComponentId = component.taskId">
                  <q-item-section :class="{'text-primary': showComponentId === component.taskId}">
                    <div class="flex items-center">
                      <!-- <span>{{index+1}}：</span> -->
                      <img v-if="component.image" :src="component.image" class="item-image q-mr-xs" alt="">
                      <div style="flex: 1;">
                        <span :title="component.taskId" style="text-overflow: ellipsis;overflow: hidden;width: 160px;">{{ component.taskId }}</span>
                        <div class="text-grey-7">
                          {{component.original_lang}}
                          <q-icon name="double_arrow"></q-icon>
                          {{component.translate_lang}}</div>
                      </div>
                    </div>
                    <div class="text-right" v-if="component.status.statu == 'pending'">
                      <div v-if="component.status.code == 1">視頻上傳中...</div>
                      <div v-if="component.status.code == 2">获取文字中...</div>
                      <div v-if="component.status.code == 3">获取翻譯中...</div>
                      <div v-if="component.status.code == 4">獲取音頻中...</div>
                      <div v-if="component.status.code == 5">獲取Lipsync中...</div>
                    </div>
                    <div class="text-right" v-if="component.status.statu == 'success'">
                      <div v-if="component.status.code == 1">視頻上傳成功</div>
                      <div v-if="component.status.code == 2">获取文字成功</div>
                      <div v-if="component.status.code == 3">获取翻譯成功</div>
                      <div v-if="component.status.code == 4">獲取音頻成功</div>
                      <div v-if="component.status.code == 5">獲取Lipsync成功</div>
                    </div>
                    <!-- <div class="text-right text-primary" v-if="component.status.code == 4 && component.status.statu == 'success'">任務完成</div> -->
                    <div class="text-right text-primary" v-if="component.status.statu == 'fail'">發生錯誤</div>
                  </q-item-section>
                  <q-item-section side>
                    <q-icon name="delete" class="delete-btn" @click.stop="delMission(component.taskId)"></q-icon>
                  </q-item-section>
                </q-item>
              </q-list>

              <div class="q-pa-md">
                <q-btn class="full-width relative-position" color="primary">
                  <input ref="uploadFile" type="file" accept=".mp4" multiple class="absolute-left upload-file" title="新增任務" @change="onMultipleFileChange" />
                  新增任務
                </q-btn>
              </div>

              <div class="q-px-md flex justify-center" style="width: 100%; position: fixed; bottom: 0; right: 0;background-color: #fff;padding: 8px 16px;">
                <q-pagination v-model="page" :max="Math.ceil(total/7)" :max-pages="6" @input="pageChange"></q-pagination>
              </div>
            </div>
          </q-scroll-area>
        </q-drawer>

        <q-dialog v-model="multipleFile.visible">
          <q-card style="max-width: 86vw;">
            <q-card-section>
              <div class="text-h6">新增任務</div>
            </q-card-section>

            <q-card-section class="q-pt-none">
              <div class="text-right">
                <q-checkbox @input="allEnabledChange" indeterminate-value="ft" v-model="allEnabled" label="所有任務直接生成音頻"></q-checkbox>
                <!-- <q-checkbox @input="allEnabledLipsyncChange" indeterminate-value="ft" v-model="allEnabledLipsync" label="所有任務開啟Lipsync"></q-checkbox> -->
              </div>

              <div v-for="item in multipleFile.files" :key="item.fileData.name" class="q-mb-sm">
                <div class="flex">
                  <video
                    style="width: 300px"
                    controls=""
                    v-bind:autoplay="false"
                    name="media"
                  >
                    <source v-bind:src="item.videoUrl" />
                  </video>

                  <div class="flex-1 q-pl-md">
                    <p class="full-width q-mb-xs text-subtitle1">{{item.fileData.name}}</p>
                    <q-toggle v-model="item.is_enabled" @input="val => isEnabledChange(val, item)" false-value="off" true-value="on" label="直接生成音頻"></q-toggle>
                    <!-- <q-toggle v-model="item.is_enabled_lipsync" :disable="item.is_enabled == 'off'" @input="isEnabledLipsyncChange" false-value="off" true-value="on" label="開啟Lipsync"></q-toggle> -->
                    <div class="flex items-center q-pt-md">
                      <q-select
                        filled
                        v-model="item.original_lang"
                        :options="langOption"
                        option-value="value"
                        option-label="label"
                        emit-value
                        map-options
                        label="視頻語言"
                        style="width: 280px;">
                      </q-select>
                      <q-icon name="compare_arrows" class="q-px-md text-h6"></q-icon>
                      <q-select
                        filled
                        v-model="item.translate_lang"
                        :options="langOption"
                        option-value="value"
                        option-label="label"
                        emit-value
                        map-options
                        label="翻譯語言"
                        style="width: 280px;">
                      </q-select>
                    </div>
                  </div>
                </div>
              </div>
            </q-card-section>

            <q-card-actions align="right">
              <q-btn label="確定" color="primary" @click="multipleFileSubmit"></q-btn>
            </q-card-actions>

            <q-inner-loading :showing="multipleFile.loading">
              <q-spinner-gears size="50px" color="primary"></q-spinner-gears>
              <h6 class="q-mt-lg">創建任務中...</h6>
            </q-inner-loading>
          </q-card>
        </q-dialog>

        <q-btn v-if="!drawerRight" class="list-btn glossy fixed" round color="primary" icon="list" @click="drawerRight = !drawerRight"></q-btn>
      </q-layout>
    </div>

    <template id="child-component-template">
      <div style="height: 100vh;">
        <div v-if="propdata" class="row justify-center q-mt-md">
          <div class="col-12 shadow-1 q-pa-md">
            <!-- <div class="row" v-if="!videoUrl">
              <div class="col-10 q-pr-lg">
                <q-input
                  class="file-main"
                  type="file"
                  v-model="selectedFile"
                  @change="onFileChange"
                />
              </div>
              <q-btn
                class="q-mt-md col-2"
                color="primary"
                label="上传"
                v-on:click="uploadFile"
                :disable="!selectedFile"
              />
            </div> -->
            <template v-if="propdata.status.code != 1 || (propdata.status.code == 1 && propdata.status.statu == 'success')">
              <div class="row justify-center relative-position">
                <video
                  style="width: 60%"
                  controls=""
                  v-bind:autoplay="false"
                  name="media"
                >
                  <source v-bind:src="propdata.video" />
                </video>

                <div class="full-width row items-center justify-center q-gutter-sm q-py-md">
                  <span class="text-h6"></span>
                  <q-radio
                    v-for="(item, index) in langOption"
                    :key="index"
                    v-model="propdata.original_lang"
                    :val="item"
                    :label="item"
                    disable
                  >
                  </q-radio>
                </div>
              </div>

              <div class="q-pt-md" v-show="fileType == '1'">
                <q-input outlined stack-label v-model="fileId" label="File Name" />
              </div>
            </template>

            <template v-if="propdata.status.code >= 2 || (propdata.status.code == 1 && propdata.status.statu == 'success')">
              <div class="q-pt-md">
                <q-btn
                  color="primary"
                  size="md"
                  disable
                  :loading="stopApiData.checkMesg.loading"
                  v-on:click="checkMesgFun"
                  >获取文字
                </q-btn>
                <q-icon
                  v-bind:class="`${ stopApiData.checkMesg.status ? 'text-positive' : 'text-grey' } text-h5 q-ml-sm`"
                  name="done_all"
                />
              </div>
              <!-- 显示队列 -->
              <!-- <div class="q-pt-md">
                <q-btn
                  color="primary"
                  size="md"
                  v-on:click="showList"
                  >展示队列
                </q-btn>
              </div> -->
            </template>

            <template v-if="propdata.status.code >= 2">
              <div class="row" v-if="propdata.original_text.length">
                <div class="col-xs-12 col-md-12 text-h5 text-primary text-center q-pt-sm">
                  Translate
                </div>
                <div class="col-xs-12 col-md-12 shadow-1 q-mt-md q-pa-sm">
                  <div class="q-gutter-sm row justify-center">
                    <q-radio
                      v-for="(item, index) in langOption"
                      :key="index"
                      v-model="propdata.translate_lang"
                      :val="item"
                      :label="item"
                    >
                    </q-radio>
                  </div>
                </div>
              </div>

              <div class="relative-position">
                <div class="q-mt-md justify-center">
                  <template v-if="propdata.original_text.length && !propdata.translate_text.length">
                    <div
                      class="row items-center"
                      v-for="(item, index) in propdata.original_text"
                      v-bind="item.id"
                    >
                      <div class="col-12 row items-center q-mb-sm q-px-md`">
                        <q-input
                          outlined
                          dense
                          autogrow
                          v-model="item.text"
                          class="col"
                          v-on:input="msgDataListEdit()"
                        >
                          <q-badge color="orange" floating style="font-size: 13px">
                            <div class="q-pr-sm">
                              開始時間: {{ item.StartTime }}s
                            </div>
                            <div class="q-pr-sm">結束時間: {{ item.EndTime }}s</div>
                          </q-badge>
                        </q-input>
                      </div>
                    </div>
                  </template>

                  <template v-if="propdata.original_text.length && propdata.translate_text.length">
                    <div>
                      <div
                        class="row"
                        v-for="(item, index) in propdata.original_text"
                        v-bind="item.id" style="display: flex;">
                        <div
                          class="col-6 row items-center q-mb-sm q-px-md left-text-item"
                        >
                          <q-input
                            outlined
                            dense
                            autogrow
                            v-model="item.text"
                            class="col"
                            v-on:input="translateMsgEdit(1)"
                          >
                            <q-badge
                              color="orange"
                              floating
                              style="font-size: 13px"
                            >
                              <div class="q-pr-sm">
                                開始時間: {{ item.StartTime }}s
                              </div>
                              <div class="q-pr-sm">
                                結束時間: {{ item.EndTime }}s
                              </div>
                            </q-badge>
                          </q-input>
                        </div>

                        <div
                          v-if="propdata.status.code >= 3 && propdata.translate_text[index]"
                          class="row items-center col-6 q-mb-sm"
                        >
                          <q-input
                            v-on:input="translateMsgEdit(2)"
                            outlined
                            dense
                            autogrow
                            v-model="propdata.translate_text[index].text"
                            class="col"
                          >
                            <q-badge
                              color="orange"
                              floating
                              style="font-size: 13px"
                            >
                              <div class="q-pr-sm">
                                開始時間: {{ propdata.translate_text[index].StartTime }}s
                              </div>
                              <div class="q-pr-sm">
                                結束時間: {{ propdata.translate_text[index].EndTime }}s
                              </div>
                            </q-badge>
                          </q-input>
                          <!-- <q-btn v-on:click="aiReprocessingContentSingle(translateMsg[index])" size="xs" color="primary">Ai重新獲取</q-btn>-->
                        </div>
                      </div>
                      <!-- <div v-if="propdata.status.code >= 3" style="display: flex; flex-direction: column; flex: 1">

                      </div> -->
                    </div>
                  </template>
                </div>
              </div>
            </template>

            <template>
              <div v-if="getAudio" class="row q-mt-md justify-center">
                <audio
                  v-bind:src="getAudio"
                  :key="getAudioKey"
                  controls
                  class="col-8"
                  style="height: 46px"
                ></audio>
              </div>

              <div v-if="propdata.status.code >= 2" class="text-center q-mt-xl">
                <q-btn
                  color="primary"
                  size="md"
                  v-if="propdata.original_text.length && !propdata.translate_text.length"
                  :loading="submitTranslateLoading"
                  v-on:click="submitTranslateFun()"
                >
                  获取翻译
                </q-btn>
              </div>

              <div v-if="propdata.status.code >= 3" class="text-center q-mt-xl">

                <!-- :loading="submitTranslateLoading || getAudioLoading" -->
                <q-btn
                  color="primary"
                  size="md"
                  v-if="propdata.translate_text.length"
                  v-on:click="tuiliSubmit"
                >
                  {{ getAudio ? '重新獲取音頻' : '獲取音頻'}}</q-btn>
                <q-btn
                  v-if="propdata.translate_text.length"
                  v-on:click="submitTranslateFun()"
                  color="primary"
                  icon="lightbulb_outline"
                  >重新翻譯
                </q-btn>
              </div>
            </template>

            <div class="q-pt-md" v-if="getVoiceList.length">
              <div class="row justify-start text-start">
                <q-radio
                  class="row justify-start col-2"
                  left-label
                  v-model="pickVoice"
                  v-bind:val="item.value"
                  v-for="item in getVoiceList"
                  >{{ item.title }}</q-radio
                >
              </div>

              <div class="text-center q-mt-xl">
                <q-btn
                  color="primary"
                  size="md"
                  :loading="getGenerateCloneLoading"
                  v-on:click="getGenerateClone"
                  >提交
                </q-btn>
              </div>
            </div>

            <div v-if="(propdata.status.code == 4 && propdata.status.statu == 'success') || propdata.status.code > 4" class="row q-mt-md justify-center">
              <audio
                v-bind:src="propdata.audio"
                :key="getAudioKey"
                controls
                class="col-8"
                style="height: 46px"
              ></audio>

             <div class="full-width q-mt-md text-center">
                <q-btn color="primary" size="md" v-on:click="submitLipsync">Lipsync</q-btn>
             </div>

             <div class="row q-mt-md justify-center full-width" v-if="propdata.status.code == 5 && propdata.status.statu == 'success'">
              <video
                style="width: 60%"
                controls=""
                v-bind:autoplay="false"
                name="media"
              >
                <source v-bind:src="propdata.lipsync" />
              </video>
             </div>
            </div>
          </div>
        </div>

        <!-- <q-inner-loading
          :showing="stopApiData.checkMesg.loading || submitTranslateLoading || aiReprocessingContentLoading || getAudioLoading || getAudioTimeLoading"
        >
          <q-spinner-ball size="50px" color="primary"></q-spinner-ball>
          <h5 class="q-mt-lg">處理中...</h5>
        </q-inner-loading> -->

        <q-inner-loading :showing="pendingLoading">
          <q-spinner-gears size="50px" color="primary"></q-spinner-gears>
          <h5 v-if="propdata?.status.code == 1" class="q-mt-lg">視頻上傳中...</h5>
          <h5 v-if="propdata?.status.code == 2" class="q-mt-lg">获取文字中...</h5>
          <h5 v-if="propdata?.status.code == 3" class="q-mt-lg">获取翻譯中...</h5>
          <h5 v-if="propdata?.status.code == 4" class="q-mt-lg">獲取音頻中...</h5>
          <h5 v-if="propdata?.status.code == 5" class="q-mt-lg">獲取Lipsync中...</h5>
        </q-inner-loading>
        <q-inner-loading :showing="details.loading">
          <q-spinner-gears size="50px" color="primary"></q-spinner-gears>
          <h5 class="q-mt-lg">獲取中...</h5>
        </q-inner-loading>
      </div>
    </template>
  </body>
  <script>
    window.quasarConfig = {
      brand: {
        primary: "#014aa1",
        secondary: "#26A69A",
        accent: "#9C27B0",

        dark: "#1d1d1d",

        positive: "#21BA45",
        negative: "#C10015",
        info: "#31CCEC",
        warning: "#F2C037",
      },
      loadingBar: { skipHijack: true },
    };
  </script>
  <script src="./plugin/quasar.umd.min.js"></script>
  <script src="./plugin/quasar.ie.polyfills.umd.min.js"></script>
  <script src="./css/quasar/zh-hant.umd.min.js"></script>
  <script>
    Quasar.lang.set(Quasar.lang.zhHant);
  </script>

  <script>
    // const LANG_OPTION = [
    //     "中文",
    //     "粵語",
    //     "英文",
    //     "越南語",
    //     "日語",
    //     "印尼語",
    //     "高棉語",
    //     "泰語",
    //     "阿拉伯文",
    //     "法文",
    //     "西班牙文",
    //     "马来文",
    //     "韩语",
    //   ]
      const LANG_OPTION = [
        "中文",
        "英文",
        "日語",
        "印尼語"
      ]

    /** 生成随机ID */
    function generateRandomId() {
      return 'ID' + Date.now() + '' + Math.floor(Math.random() * 10000);
    }

    Vue.component('child-component', {
      template: '#child-component-template',
      props: {
        propvalue: {
          type: Object
        }
      },
      watch: {
        // propvalue: {
        //   handler(newVal) {
        //     this.propdata.status = newVal.status
        //   },
        //   immediate: false
        // },
        // 'propvalue.status': {
        //   handler(newVal) {
        //     if (this.propdata.status.code != this.propvalue.status.code || this.propdata.status.statu != this.propvalue.status.statu) {
        //       console.log(this.propdata.status, 'propdata', this.propvalue.status, 'propvalue')
        //       this.propdata = this.propvalue
        //     }
        //   },
        //   immediate: false
        // }
      },
      computed: {
        pendingLoading() {
          return this.propdata?.status?.statu === 'pending' || false
        }
      },
      data: function () {
        return {
          propdata: null,
          langOption: LANG_OPTION,
          items: [],
          currentAudio: null,
          selectedFile: null,
          upFileLoading: false,
          fileId: "",
          videoUrl: null,
          fileName: "",
          fileType: "0",
          langObj: {
            form: "zh",
            to: "中文",
          },
          msg: "",
          msgDataList: [
            // {
            //     id: "39",
            //     data: "谢谢你。",
            //     allTime: "1.00",
            //     voiceTime: "0.60",
            //     videoUrl: "",
            // },
          ],
          upMsgDataList: [], // 視頻原文內容(有修改需要更新的)
          translateMsg: [
            // {
            //     "id": "39",
            //     "data": "谢谢你。",
            //     "allTime": "1.00",
            //     "voiceTime": 3.875,
            //     videoUrl: ''
            // }
          ], // 翻譯後內容
          upTranslateMsg: [], // 翻譯後內容(有修改需要更新的)
          aiReprocessingContentLoading: false, // AI重新處理內容Loading狀態
          getAudio: null,
          getAudioKey: 1,
          getAudioLoading: false,
          audioType: "1",
          VoiceName: null,
          getVoiceNameLoading: false,
          getVoiceList: [],
          pickVoice: "",
          getGenerateCloneLoading: false,
          getGenerateCloneUrl: null,
          stopApiData: {
            checkMesg: {
              loading: false,
              status: false,
            },
          },
          submitTranslateLoading: false,
          getAudioStatusTimeout: null, // 語音生成狀態定時器
          getGenerateCloneStatusTimeout: null, // 語音克隆狀態定時器
          getAudioTimeLoading: false,
          videoLang: "", // 上傳視頻的語言
          // 2024-01-13
          taskId: null,
          audioAddress: "",
          changeType: {
            origin: false,
            translate: false,
          },
          asyncMsgTimeout: null,
          asyncGenAudio: null,
          details: {
            loading: false
          },
          asyncDatails: null
        };
      },
      methods: {
        /** 弹框函数包装
         * @param {string} _message: 提示消息
         * @param {string} _color: 提示消息框背景色
         * @param {string} _position: 提示消息框弹出位置
         */
        notifyCustom(_message, _color, _position) {
          this.$q.notify({
            message: _message,
            color: _color,
            position: _position || "top",
          });
        },

        // 上传文件内容获取
        onFileChange(e) {
          // 你可以在这里对文件进行预处理，比如大小检查、类型检查等
          this.selectedFile = e.target.files[0];
          // this.$emit('child-event', this.selectedFile)
        },

        // 上传文件
        uploadFile(data) {
          let _this = this;
          const { fileData, original_lang, translate_lang, taskId } = data
          console.log(data, 111)
          _this.selectedFile = fileData
          if (!_this.selectedFile) return; // 如果没有选择文件，则不执行上传

          // 创建一个 FormData 实例
          const formData = new FormData();
          const _fileNameType =
            _this.selectedFile.name.split(".")[
              _this.selectedFile.name.split(".").length - 1
            ];
          // const taskId = String(Date.now());
          const newFileName = taskId + "." + _fileNameType;
          _this.taskId = taskId;
          _this.fileName = newFileName;
          formData.append("file", _this.selectedFile, _this.fileName);
          formData.append("taskId", taskId);
          formData.append("fileName", newFileName);
          formData.append("original_lang", newFileName);
          formData.append("translate_lang", newFileName);
          // status:{
    			// 	code: 1,
    			// 	statu: success(执行成功) fail(失败) pending(运行中)
    			// 	}
          _this.upFileLoading = true;
          $.ajax({
            url: `${PUBLIC_BASE_URL}/ai/upload/1`,
            data: formData,
            type: "POST",
            processData: false,
            contentType: false,
            success: function (res) {
              // _this.fileId = res.videoUrl;
              // _this.videoUrl = res.videoUrl;
              if (res.statusCode !== 200) {
                _this.notifyCustom(res.statusMessage || "错误", "red");
                return;
              } else {
                const videoURL = URL.createObjectURL(_this.selectedFile);
                _this.videoUrl = videoURL;

                _this.notifyCustom(res.statusMessage || "成功", "positive");
                _this.$emit('change-img', { imageUrl: res.imageUrl, taskId } )
              }
            },
            complete: function () {
              // 清除文件选择，以便用户可以选择其他文件
              _this.selectedFile = null;
              _this.upFileLoading = false;
            },
          });
        },

        // 获取文件文字
        checkMesgFun() {
          let _this = this;

          if (!_this.propdata.original_lang) {
            _this.notifyCustom("請選擇視頻語言", "red");
            return false;
          }

          this.details.loading = true
          _this.stopApiData.checkMesg.loading = true;
          _this.details.loading = true;
          const _data = {
            fileName: _this.fileName,
            language: _this.propdata.original_lang,
            taskId: _this.taskId,
          };
          $.ajax({
            url: `${PUBLIC_BASE_URL}/ai/GetMessage`,
            method: "POST",
            data: JSON.stringify(_data),
            headers: { "content-type": "application/json" },
            success: function (res) {
              if (res.statusCode !== 200) {
                _this.notifyCustom("错误", "red");
                _this.stopApiData.checkMesg.loading = false;
                _this.stopApiData.checkMesg.status = res.statusCode === 200 ? true : false;
                return;
              }

              setTimeout(() => {
                _this.getDatails()
              }, 3000)
              // _this.getAsyncMsgFunc({ taskId: res.taskId })
              // _this.notifyCustom("成功", "positive");
            },
            error: function() {
              _this.details.loading = true;
            },
            complete: function() {
              _this.stopApiData.checkMesg.loading = false;
            }
          });
        },

        getAsyncMsgFunc({ taskId }) {
          const _this = this
          const _data = {
            fileName: _this.fileName,
            language: _this.propdata.original_lang,
            taskId,
          };

          _this.asyncMsgTimeout = setInterval(() => {
            $.ajax({
              url: `${PUBLIC_BASE_URL}/ai/GetMessage`,
              type: "GET",
              data: _data,
              headers: { "content-type": "application/json" },
              dataType: "json",
              success: function (res) {
                const { state, statusCode, result = {} } = res
                  const { statusMessage } = result
                  const changeData = (code) => {
                    _this.stopApiData.checkMesg.loading = false;
                     _this.stopApiData.checkMesg.status = code === 200 ? true : false;
                  }

                if (state === 'FAILURE') {
                  _this.notifyCustom("错误", "red");
                  changeData(statusCode)
                  _this.asyncMsgTimeout && clearInterval(_this.asyncMsgTimeout)
                }

                if (state === 'SUCCESS') {
                  _this.asyncMsgTimeout && clearInterval(_this.asyncMsgTimeout)
                  changeData(statusCode)
                  _this.propdata.original_text = statusMessage;
                  _this.notifyCustom("成功", "positive");
                }
              }
            });
          }, 2000)
        },

        /** AI重新處理內容（單個）
         * @param {onject} data: 翻譯內容
         */
        aiReprocessingContentSingle(translatePickData) {
          let _this = this;
          let _param = JSON.stringify({
            fileName: _this.fileName.split(".")[0],
            data: [translatePickData],
          });

          _this.aiReprocessingContentLoading = true;
          $.ajax({
            url: `/ai/aiGenerateText`,
            method: "post",
            data: _param,
            dataType: "json",
            contentType: "application/json; charset=utf8",
            success: function (res) {
              if (res.statusCode !== 200) {
                _this.notifyCustom("AI重新處理內容失败", "red");
              } else {
                let _findDataRow = _this.propdata.translate_text.find(
                  (findItem) => findItem.id == translatePickData.id
                );
                console.log(_findDataRow, res.statusMessage);
                if (_findDataRow) _findDataRow.data = res.statusMessage.data;

                _this.upTranslateMsg = _this.upTranslateMsg.filter(
                  (item) => item.id != translatePickData.id
                ); // 接口會自動處理保存，在 翻譯後內容(有修改需要更新的) 中刪除
                _this.notifyCustom("AI重新處理內容成功", "positive");
              }
            },
            error: function (err) {
              _this.notifyCustom("AI重新處理內容失败", "red");
            },
            complete: function () {
              _this.aiReprocessingContentLoading = false;
            },
          });
        },

        /** AI重新處理內容（所有）
         * @param {onject} data: 翻譯內容
         */
        aiReprocessingContentAll() {
          let _this = this;

          // 注：upTranslateMsg(翻譯內容有更改的話，需要先調用保存接口，再Ai生成)
          _this.aiReprocessingContentLoading = true;
          if (_this.upTranslateMsg.length == 0) {
            $.ajax({
              url: `/ai/aiGenerateText?fileName=${
                _this.fileName.split(".")[0]
              }`,
              method: "GET",
              success: function (res) {
                if (res.statusCode !== 200) {
                  _this.notifyCustom("AI重新處理內容失败", "red");
                } else {
                  _this.propdata.translate_text = res.statusMessage;
                  _this.propdata.translate_text = _this.propdata.translate_text.sort((a, b) => {
                    return Number(a.id) - Number(b.id);
                  });
                  _this.upTranslateMsg = []; // 重置修改翻譯內容為空
                  _this.notifyCustom("AI重新處理內容成功", "positive");
                }
              },
              error: function (err) {
                _this.notifyCustom("AI重新處理內容失败", "red");
              },
              complete: function () {
                _this.aiReprocessingContentLoading = false;
              },
            });
          } else {
            let _param = JSON.stringify({
              fileName: _this.fileName.split(".")[0],
              newDataList: _this.upTranslateMsg,
              typeId: 2,
              language: _this.propdata.translate_lang,
            });

            _this.upFanyiApi(
              _param,
              function (res) {
                if (res.statusCode !== 200) {
                  _this.notifyCustom(
                    res.statusMessage || "更新翻譯失败",
                    "red"
                  );
                  _this.aiReprocessingContentLoading = false;
                } else {
                  _this.upTranslateMsg = []; // 重置修改翻譯內容為空
                  _this.aiReprocessingContentAll();
                }
              },
              function () {
                _this.notifyCustom(res.statusMessage || "更新翻譯失败", "red");
                _this.aiReprocessingContentLoading = false;
              }
            );
          }
        },

        // 視頻原文內容有修改需要保存提交更新
        msgDataListEdit(data) {
          let _this = this;
          _this.changeType.origin = true;
        },

        // 翻譯後內容有修改需要保存提交更新
        translateMsgEdit(type) {
          let _this = this;
          //
          if (type === 1) {
            _this.changeType.origin = true;
          } else {
            _this.changeType.translate = true;
          }
        },

        // 获取翻译时长
        getAudioTimeFun(row) {
          let _this = this;

          _this.getAudioTimeLoading = true;
          let _param = JSON.stringify({
            fileName: _this.fileName.split(".")[0],
            newDataList: [row],
            typeId: 2,
            language: _this.propdata.translate_lang,
          });
          _this.upFanyiApi(
            _param,
            function (res) {
              _this.getAudioTimeLoading = false;
              if (res.statusCode !== 200) {
                _this.notifyCustom(res.statusMessage || "獲取時長失败", "red");
              } else {
                _this.upTranslateMsg = _this.upTranslateMsg.filter(
                  (item) => item.id != row.id
                );
                res.statusMessage?.forEach((item) => {
                  let _findDataRow = _this.propdata.translate_text.find(
                    (findItem) => findItem.id == item.id
                  );
                  if (_findDataRow)
                    _findDataRow.voiceTime = Number(item.voiceTime);
                });
              }
            },
            function () {
              _this.getAudioTimeLoading = false;
            }
          );
        },

        getAudioTime(pickData) {
          let _this = this;
          let timeFun = (_dataJson) => {
            $.ajax({
              url: `/ai/getAudioTime`,
              method: "post",
              data: JSON.stringify({ data: _dataJson }),
              dataType: "json",
              contentType: "application/json; charset=utf8",
              success: function (res) {
                if (res.statusCode !== 200) {
                  _this.notifyCustom("获取内容时长失败", "red");
                } else {
                  res.statusMessage?.forEach((item) => {
                    let _findDataRow = _this.propdata.translate_text.find(
                      (findItem) => findItem.id == item.id
                    );
                    if (_findDataRow) _findDataRow.voiceTime = item.voiceTime;
                  });
                }
              },
              error: function (err) {
                _this.notifyCustom("获取内容时长失败", "red");
              },
              complete: function () {
                let _findDataRow = _this.propdata.translate_text.findIndex(
                  (findItem) => findItem.id == 2
                );
                if (_findDataRow != -1)
                  _this.$set(_this.propdata.translate_text[_findDataRow], "voiceTime", 20);
                console.log(_this.propdata.translate_text);
              },
            });
          };
          if (pickData) {
            timeFun([pickData]);
          } else {
            timeFun(_this.propdata.translate_text);
          }
        },

        // 新增下载方法
        downloadAudio(item) {
          // 统计下载次数
          item.downloadCount++

          // 创建隐藏的下载链接
          const link = document.createElement('a')
          link.href = item.audioUrl
          link.download = `${item.name}.mp3`
          link.style.display = 'none'

          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)

          // 可选：添加下载完成提示
          setTimeout(() => {
            if (!link.href.startsWith('blob:')) {
              alert('下载已开始，请检查浏览器下载列表')
            }
          }, 100)
        },

        /** 同步Api翻譯內容
         * @param {object} _param: 參數
         * @param {object} callback: 回調函數
         * @param {object} errorCallback: 錯誤回調函數
         */
        upFanyiApi(_param, callback, errorCallback) {
          $.ajax({
            url: "/ai/fanyi",
            method: "post",
            data: _param,
            dataType: "json",
            contentType: "application/json; charset=utf8",
            success: function (res) {
              callback && callback(res);
            },
            error: function (err) {
              errorCallback && errorCallback();
            },
            complete: function () {},
          });
        },

        /** 提交翻译数据
         * @param {number} reFanyi: 0 初次獲取翻譯 1 重新獲取翻譯
         */
        submitTranslateFun() {
          let _this = this;

          const { taskId, langObj, changeType } = _this;

          const queues = [];
          if (changeType.origin) {
            queues.push(
              _this.onUploadText({
                type: 1,
                taskId,
                updateList: _this.propdata.original_text,
              })
            );
          }

          _this.submitTranslateLoading = true;
          _this.details.loading = true;
            Promise.all(queues)
              .then(() => {
                $.ajax({
                  url: `${PUBLIC_BASE_URL}/ai/translate`,
                  data: JSON.stringify({
                    language: _this.propdata.translate_lang,
                    taskId: _this.taskId,
                  }),
                  method: "POST",
                  headers: { "content-type": "application/json" },
                  success: function (res) {
                    if (res.code !== 200) {
                      _this.notifyCustom(res.statusMessage || "失败", "red");
                    } else {
                      setTimeout(() => {
                        _this.getDatails()
                      }, 3000)
                      _this.upTranslateMsg = []; // 重置修改翻譯內容為空
                      // _this.getAudioTime()
                      _this.notifyCustom("成功", "positive");
                    }
                  },
                  error: function (err) {
                    _this.notifyCustom("失败", "red");
                  },
                  complete: function () {
                    _this.submitTranslateLoading = false;
                  },
                });
              })
              .catch(() => {
                _this.notifyCustom("失败", "red");
                _this.details.loading = false;
              }).finally(()=>{
                _this.changeType.origin = false;
              });
        },

        // 獲取音頻
        getAiGenerate() {
          let _this = this;

          const { taskId, langObj, changeType } =
            _this;

          const queues = [];

          if (changeType.origin) {
            queues.push(
              _this.onUploadText({
                type: 1,
                taskId,
                updateList: _this.propdata.original_text,
              })
            );
          }

          if (changeType.translate) {
            queues.push(
              _this.onUploadText({
                type: 2,
                taskId,
                updateList: _this.propdata.translate_text,
              })
            );
          }
          _this.details.loading = true;
          Promise.all(queues)
            .then(() => {
              const data = {
                taskId,
                language: _this.propdata.translate_lang,
              };
              $.ajax({
                // url: `${PUBLIC_BASE_URL}/ai/genAudio`,
                url: `${PUBLIC_BASE_URL}/ai/clone`,
                method: "POST",
                data: JSON.stringify(data),
                headers: { "content-type": "application/json" },
                success: function (res) {
                  if (res.code != 200) {
                    _this.notifyCustom(
                      res.statusMessage || "獲取音頻失败",
                      "red"
                    )
                  } else {
                    setTimeout(() => {
                      _this.getDatails()
                    }, 3000)
                  }
                },
                error: function (err) {
                  _this.notifyCustom("失败", "red");
                  _this.getAudioLoading = false;
                  _this.details.loading = false;
                },
                complete: function () {
                  _this.changeType = {
                    origin: false,
                    translate: false,
                  };
                  // _this.getAudioLoading = false;
                },
              });
            })
            .catch(() => {
              _this.notifyCustom("失败", "red");
            });
        },

        tuiliSubmit() {
          let _this = this;

          _this.getAudioLoading = true;
          _this.details.loading = true;
          if (_this.upTranslateMsg.length > 0) {
            // 是否修改了翻譯內容，修改了重新提交更新
            let _param = JSON.stringify({
              fileName: _this.fileName.split(".")[0],
              newDataList: _this.upTranslateMsg,
              typeId: 2,
              language: _this.propdata.translate_lang,
            });
            _this.upFanyiApi(_param, function (res) {
              if (res.statusCode !== 200) {
                _this.notifyCustom(res.statusMessage || "更新翻譯失败", "red");
              } else {
                _this.upTranslateMsg = []; // 重置修改翻譯內容為空
                _this.getAiGenerate();
              }
            });
          } else {
            _this.getAiGenerate();
          }
        },

        submitLipsync() {
          const _this = this
          _this.details.loading = true
          $.ajax({
            url: `${PUBLIC_BASE_URL}/ai/lipsync`,
            method: "POST",
            data: JSON.stringify({
              taskId: _this.taskId
            }),
            headers: { "content-type": "application/json" },
            success: function (res) {
              if (res.code != 200) {
                _this.notifyCustom(
                  res.statusMessage || "Lipsync 失败",
                  "red"
                )
                _this.details.loading = false;
              } else {
                setTimeout(() => {
                  _this.getDatails()
                }, 3000)
              }
            },
            error: function (err) {
              _this.notifyCustom("Lipsync 失败", "red");
              _this.details.loading = false;
            }
          });
        },

        // text translate audio
        async onUploadText(data) {
          try {
            const _this = this;
            // /ai/uploadText
            const res = await fetch(`${PUBLIC_BASE_URL}/ai/upload/text`, {
              method: "POST",
              body: JSON.stringify(data),
              headers: {
                "Content-Type": "application/json",
              },
            });
            const result = await res.json();
            return result;
          } catch (error) {
            console.log("error", error);
          }
        },

        async getDatails() {
          let result = await axios({
            method:'get',
              url: `${PUBLIC_BASE_URL}/ai/task/list?taskId=${this.taskId}`
          })
          this.details.loading = false
          this.propdata = result.data || null
          if ((this.propdata.status.statu == 'success' && this.propdata.status.code == 4) || this.propdata.status.statu == 'fail') {
            this.asyncDatails && clearInterval(this.asyncDatails)
          }
          if (this.propdata.status.statu == 'pending') {
            this.asyncDatails = setTimeout(() => {
              this.getDatails()
            }, 2000)
          }
        }
      },
      beforeDestroy() {
        console.log("離開...");
        this.asyncMsgTimeout && clearInterval(this.asyncMsgTimeout)
        this.asyncGenAudio && clearInterval(this.asyncGenAudio)
        this.asyncDatails && clearInterval(this.asyncDatails)
      },
      mounted() {
        this.taskId = this.propvalue.taskId
        // this.propdata = JSON.parse(JSON.stringify(this.propvalue))

        this.details.loading = true
        this.getDatails()
      }
    })

    new Vue({
      el: "#app",
      data: {
        langOption: LANG_OPTION.map(item => {
          return {
            value: item,
            label: item
          }
        }),
        allEnabled: false,
        allEnabledLipsync: false,
        multipleFile: {
          visible: false,
          files: null,
          _files: null,
          loading: false
        },
        components: [],
        drawerRight: true,
        totalCount: 0,
        showComponentId: null,
        asyncTaskList: null,
        page: 1,
        total: 0,
        jqXHR: null
      },
      watch: {
        // 'multipleFile.files': {
        //   deep: true,
        //   handler(newVal) {
        //     console.log(newVal, 'newVal')

        //   },
        //   immediate: false
        // }
      },
      methods: {
        isEnabledChange(val, changeItem) {
          // 检查是否所有文件的 is_enabled 都是 'off' 或 'no'
          const allDisabled = this.multipleFile.files.every(item =>
            item.is_enabled === 'off' || item.is_enabled === 'no'
          );

          // 检查是否所有文件的 is_enabled 都是 'on' 或 'yes'
          const allEnabled = this.multipleFile.files.every(item =>
            item.is_enabled === 'on' || item.is_enabled === 'yes'
          );

          if (val == 'off') changeItem.is_enabled_lipsync =  'off'

          // 更新 allEnabled 值
          this.allEnabled = allEnabled ? true : (allDisabled ? false : 'ft');

          this.isEnabledLipsyncChange()
        },

        isEnabledLipsyncChange() {
           // 检查是否所有文件的 is_enabled 都是 'off' 或 'no'
          const allDisabled = this.multipleFile.files.every(item =>
            item.is_enabled_lipsync === 'off' || item.is_enabled_lipsync === 'no'
          );

          // 检查是否所有文件的 is_enabled 都是 'on' 或 'yes'
          const allEnabled = this.multipleFile.files.every(item =>
            item.is_enabled_lipsync === 'on'
          );

          // 更新 allEnabled 值
          this.allEnabledLipsync = allEnabled ? true : (allDisabled ? false : 'ft')
        },

        allEnabledChange(val) {
          this.multipleFile.files.forEach(item => {
            this.$set(item, 'is_enabled', val ? 'on' : 'off')
            if (!val) this.$set(item, 'is_enabled_lipsync', 'off')
          })

          if (!val) {
            this.allEnabledLipsync = false
          }
        },

        allEnabledLipsyncChange(val) {
          this.multipleFile.files.forEach(item => {
            this.$set(item, 'is_enabled_lipsync', val ? 'on' : 'off')
            if (val) this.$set(item, 'is_enabled', 'on')
          })

          const allDisabled = this.multipleFile.files.every(item =>
            item.is_enabled === 'off' || item.is_enabled === 'no'
          );

          // 检查是否所有文件的 is_enabled 都是 'on' 或 'yes'
          const allEnabled = this.multipleFile.files.every(item =>
            item.is_enabled === 'on' || item.is_enabled === 'yes'
          );

          // 更新 allEnabled 值
          this.allEnabled = allEnabled ? true : (allDisabled ? false : 'ft');
        },

        async onMultipleFileChange(e) {
          const files = Array.from(e.target.files) || []

          this.allEnabled = false
          this.allEnabledLipsync = false
          this.multipleFile.files = files.map(item => {
            // 创建一个新对象并添加 videoUrl 属性
            const newItem = { fileData: item }
            this.$set(newItem, 'videoUrl', URL.createObjectURL(item))
            this.$set(newItem, 'original_lang', '中文')
            this.$set(newItem, 'translate_lang', '中文')
            this.$set(newItem, 'taskId', generateRandomId())
            this.$set(newItem, 'is_enabled', 'off')
            this.$set(newItem, 'is_enabled_lipsync', 'off')
            // this.$set(newItem, 'status', 1)
            return newItem
          })

          await this.$nextTick()
          this.multipleFile.visible = true

          if (this.$refs['uploadFile']) this.$refs['uploadFile'].value = ''
        },
        async multipleFileSubmit() {
          let _this = this
          _this.multipleFile.loading = true
          // _this.multipleFile._files = _this.multipleFile.files

          try {
            // 创建上传任务队列
            const uploadTasks = this.multipleFile.files.map(file => {
              return this.uploadFile_1(file)
            })

            // 并行执行所有上传任务并等待全部完成
            const results = await Promise.all(uploadTasks)

            // 所有上传都成功完成
            this.$q.notify({
              message: '所有文件上传成功',
              color: 'green',
              position: 'top'
            })

            // 处理所有上传结果
            console.log('所有上传结果:', results);
            return results;
          } catch (errors) {
            // 至少有一个上传失败
            this.$q.notify({
              message: '部分任務創建失败',
              color: 'red',
              position: 'top'
            });

            // 处理错误数组
            console.error('上传错误:', errors);
            throw errors; // 可选：将错误继续抛出
          } finally {
            _this.multipleFile.loading = false
            _this.multipleFile.visible = false
            _this.page = 1
          }
        },
        async uploadFile_1(data) {
          const _this = this;
          try {
            const { fileData, original_lang, translate_lang, taskId, is_enabled, is_enabled_lipsync } = data
            // 创建一个 FormData 实例
            const formData = new FormData();
            const _fileNameType =
            fileData.name.split(".")[fileData.name.split(".").length - 1];
            const newFileName = taskId + "." + _fileNameType;
            _this.taskId = taskId;
            _this.fileName = newFileName;
            formData.append("file", fileData, _this.fileName);
            formData.append("taskId", taskId);
            formData.append("fileName", newFileName);
            formData.append("original_lang", original_lang);
            formData.append("translate_lang", translate_lang);
            formData.append("is_enabled", is_enabled);
            formData.append("is_enabled_lipsync", is_enabled_lipsync);

            const res = await fetch(`${PUBLIC_BASE_URL}/ai/upload`, {
              method: "POST",
              body: formData
            });
            const result = await res.json();
            return result;
          } catch (error) {
            // _this.$q.notify({
            //   message:"错误",
            //   color: 'red',
            //   position: "top",
            // })
            throw error
          }
        },

        addComponent() {
          const id = generateRandomId()
          this.components.push({ id })
          this.showComponentId = id
        },
        handleChildEvent(id, data) {
          const { name } = data
          const findData = this.components.find(item => item.taskId === id)
          if (findData) {
            this.$set(findData, 'name', name)
          }
        },
        handleChildImg(id, data) {
          const { imageUrl, taskId } = data
          const findData = this.components.find(item => item.taskId === id)
          if (findData) {
            this.$set(findData, 'imageUrl', imageUrl)
            this.$set(findData, 'name', taskId)
          }
        },
        delMission(taskId) {
          const _this = this
          _this.$q.dialog({
            title: '刪除',
            message: '確認刪除此任務?',
            ok: {
              push: true
            },
            cancel: {
              push: true,
              color: 'negative'
            },
            persistent: true
          }).onOk(() => {
            $.ajax({
              url: `${PUBLIC_BASE_URL}/ai/task/delete`,
              method: "POST",
              data: JSON.stringify({ taskId }),
              headers: { "content-type": "application/json" },
              success: function (res) {
                if (res.code !== 200) {
                  _this.$q.notify({
                    message:"刪除任務失败",
                    color: 'red',
                    position: "top",
                  })
                } else {
                  _this.$q.notify({
                    message:"刪除任務成功",
                    color: 'positive',
                    position: "top",
                  })
                  if (_this.showComponentId == taskId) _this.showComponentId = null
                }
              },
              error: function (err) {
                _this.$q.notify({
                  message:"刪除任務失败",
                  color: 'red',
                  position: "top",
                })
              }
            });
          }).onCancel(() => {
            // console.log('>>>> Cancel')
          })
        },

        async getTaskList() {
          const { page } = this
          const _this = this
          _this.jqXHR = $.ajax({
            url: `${PUBLIC_BASE_URL}/ai/task/list?page=${page}`,
            method: "get",
            success: function (result) {
              _this.multipleFile._files = result?.task_list || []
              _this.total = result?.total || 0
            }
          })
          // var result = await axios({
          //   method:'get',
          //   url: `${PUBLIC_BASE_URL}/ai/task/list?page=${page}`,
          // })
          // this.multipleFile._files = result?.data?.task_list || []
          // this.total = result?.data?.total || 0
        },

        pageChange(val) {
          const _this = this
          _this.jqXHR?.abort()
          _this.getTaskList()

          _this.asyncTaskList && clearInterval(_this.asyncTaskList)
          _this.asyncTaskList = setInterval(() => {
            _this.getTaskList()
          }, 2000)
        },

        handleBeforeonload(event) {
          event.returnValue = '您确定要离开此页面吗？未保存的更改将会丢失。'
          return '您确定要离开此页面吗？未保存的更改将会丢失。'
        }
      },
      created() {
        let _this = this
        this.getTaskList()
        this.asyncTaskList = setInterval(() => {
          this.getTaskList()
        }, 2000)

        document.addEventListener('visibilitychange', function () {
          if (document.hidden) {
            _this.asyncTaskList && clearInterval(_this.asyncTaskList)
            console.log('页面隐藏');
          } else {
            _this.getTaskList()
            _this.asyncTaskList = setInterval(() => {
              _this.getTaskList()
            }, 2000)
            console.log('页面可见');
          }
        })

        window.addEventListener('beforeunload', this.handleBeforeonload)
      },
      beforeDestroy() {
        console.log("離開...");
        this.asyncTaskList && clearInterval(this.asyncTaskList)
        window.removeEventListener('beforeunload', this.handleBeforeonload)
      },
    });
  </script>
</html>

/* 弹窗容器 */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1000px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    opacity: 0;
    transition: opacity 0.3s, transform 0.3s;
    z-index: 1000;
    padding: 20px;
    visibility: hidden;
  }
  .modal.show {
    opacity: 1;
    visibility: visible;
    transform: translate(-50%, -50%) scale(1);
  }
  /* 遮罩层 */
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.5);
    z-index: 999;
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none;
  }
  .overlay.show {
    opacity: 1;
    pointer-events: all;
  }
  /* 列表样式 */
  ul {
    list-style: none;
    padding: 0;
    margin: 15px 0;
  }
  li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
  }
  .close-btn {
    float: right;
    padding: 5px 12px;
    background: #f0f0f0;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }


  /* 列表项容器 */
  .list-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background 0.2s;
  }
  .list-item:hover {
    background: #f8f8f8;
  }

  /* 左侧内容 */
  .left-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  .item-name {
    font-weight: bold;
    color: #333;
  }
  .item-image {
    width: 60px;
    height: 40px;
    border-radius: 4px;
    object-fit: cover;
  }

  /* 右侧状态 */
  .status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
  }
  .status.success {
    background: #e8f5e9;
    color: #2e7d32;
  }
  .status.fail {
    background: #ffebee;
    color: #c62828;
  }
  .status.processing {
    background: #fff3e0;
    color: #ef6c00;
  }


  .list-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #eee;
  }

  .left-content {
    flex: 2;
    display: flex;
    flex-direction: column;
  }

  .audio-controls {
    flex: 3;
    padding: 0 20px;
    
  }

  .progress-bar {
    height: 4px;
    background: #ddd;
    border-radius: 2px;
    margin-bottom: 8px;
  }

  .progress {
    height: 100%;
    background: #42b983;
    border-radius: 2px;
    transition: width 0.3s;
  }
  .butdiv{
    justify-content: center;
    align-items: center;
    margin:  0 calc(50% - 15px);
    color: #ef6c00;
    width: 100%;
  }

  .play-button {
    
    width: 30px;
    height: 30px;
    border: none;
    background: #42b983;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    
  }

  .status {
    flex: 1;
    text-align: center;
    padding: 0 10px;
  }

  .status.success { color: #42b983; }
  .status.failed { color: #ff4757; }
  .status.processing { color: #2f3542; }


  /* 新增下载按钮样式 */
  .download-section {
    flex: 1;
    padding: 0 15px;
    text-align: center;
  }
  .download-button {
    background: none;
    border: 1px solid #42b983;
    color: #42b983;
    padding: 5px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
  }
  .download-button:hover {
    background: #42b983;
    color: white;
  }
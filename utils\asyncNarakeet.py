import asyncio
import logging
import os
import random
import time

import aiofiles
import aiohttp

from config import Config

# 日志信息配置
cur_dir = os.path.abspath(__file__).rsplit("\\", 2)[0]
log_path = os.path.join(cur_dir, "logs", "narakeet_api.log")
logging.basicConfig(filename=log_path, level=logging.DEBUG,
					filemode='w', format='%(levelname)s:%(asctime)s:%(message)s', datefmt='%Y-%d-%m %H:%M:%S')

allVoiceName = {
	"中文": 'Yifei',
	"英文": 'Beatrice',
	"高棉語": 'Sovath',
	"粵語": 'Man-chi',
	"越南語": "Nga",
	"日語": "Hideaki",
	"印尼語": "Agung",
	"泰語": "Somsak",
	"阿拉伯文": "Farah",
	"法文": "<PERSON>",
	"西班牙文": "<PERSON>",
	"马来文": "<PERSON><PERSON>",
	"韩语": "Dong-min",

}


async def request_audio_task(data, language):
	print("data：", data)
	print("language：", language)
	logging.debug(f"开始执行 text narakeet === data:{data} === language:{allVoiceName[language]}")
	url = f'https://api.narakeet.com/text-to-speech/m4a?voice={allVoiceName[language]}'
	headers = {
		'Content-Type': 'text/plain',
		'x-api-key': Config.narakeet_key,
	}
	data = data.encode('utf8')
	try:
		async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(verify_ssl=False)) as request:
			async with request.post(url, headers=headers, data=data) as response:
				task_url = await response.json(content_type=None)
				print("request_audio_task --- task_url：", task_url)
				return task_url
	except Exception as e:
		logging.error(f"{data} 出现错误 === 原因：{e}")
		print(f"request narakeet fail reason is ", e)


async def request_vtt_task(data, language):
	url = f'https://api.narakeet.com/text-to-speech/mp3?voice={allVoiceName[language]}&voice-speed=1'
	# url = f'https://api.narakeet.com/text-to-speech/mp3?voice=mickey&voice-speed=1'
	print("request_vtt_task url --- >>>", url)
	print("request_vtt_task data --- >>>", data)
	print("request_vtt_task language --- >>>", language)
	options = {
		'headers': {
			'Content-Type': 'text/vtt',
			'x-api-key': Config.narakeet_key,
		},
		'data': data.encode('utf8')
	}

	timeout = aiohttp.ClientTimeout(total=30)  # 总超时30秒
	try:
		async with aiohttp.ClientSession(timeout=timeout, connector=aiohttp.TCPConnector(verify_ssl=False)) as session:
			async with session.post(url, **options) as response:
				response.raise_for_status()
				responseData = await response.json(content_type=None)
				print("✅ responseData =", responseData)
				return responseData
	except Exception as e:
		print("❌ 请求失败:", e)
		return None


# async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(verify_ssl=False)) as request:
# 	async with request.post(url, **options) as response:
# 		response.raise_for_status()
# 		responseData = await response.json()


async def poll_until_finished(task_url):
	try:
		async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(verify_ssl=False)) as request:
			while True:
				async with request.get(task_url, timeout=30000) as response:
					data = await response.json(content_type=None)
					print("poll_data:", data)
					if data.get('finished', False):
						return data
					await asyncio.sleep(3)
	except Exception as e:
		print("narrket 出现报错啦！！！", e)
		return e


async def download_to_file(url, file_path):
	timeout = aiohttp.ClientTimeout(total=60)  # 设置总超时时间为 60 秒

	for attempt in range(3):
		try:
			async with aiohttp.ClientSession(timeout=timeout,
											 connector=aiohttp.TCPConnector(verify_ssl=False)) as session:
				async with session.get(url) as response:
					response.raise_for_status()
					content = await response.read()

					async with aiofiles.open(file_path, "wb") as f:
						await f.write(content)
			logging.debug(f"成功下载: {file_path}")
			print(f"✅ 成功下载: {file_path}")
			return  # 成功退出
		except (aiohttp.ClientError, asyncio.TimeoutError, ConnectionResetError) as e:
			logging.debug(f"第 {attempt + 1} 次下载失败: {str(e)}")
			print(f"⚠️ 第 {attempt + 1} 次下载失败: {str(e)}")
			await asyncio.sleep(5)
		except Exception as e:
			logging.debug(f"下载失败 未知错误: {str(e)}")
			print(f"❌ 未知错误: {str(e)}")
			break

	print(f"❌ 最终仍然下载失败: {url}")


async def narakeetRun(type, taskID, language, number=None, data=None):
	await asyncio.sleep(random.randint(1, 10))
	if type == 'text':
		task_url = await request_audio_task(data, language)
	else:
		task_url = await request_vtt_task(data, language)
	"""
	taskUrl： {'message': 'Too Many Requests'}
	"""
	print("taskUrl：", task_url)
	logging.debug(f"{data} 获取到的task_url = {task_url}")
	status_url = task_url.get('statusUrl')
	if not status_url:
		logging.debug(f"{data} 找不到statusUrl、正在执行重试")
		print(f"{data} 找不到statusUrl、正在执行重试")
		await asyncio.sleep(random.randint(1, 10))
		await narakeetRun(type, taskID, language, number, data)
		return 200

	task_result = await poll_until_finished(status_url)
	logging.debug(f"{data} 处理task_result = {task_result}")
	print("task_result", task_result)
	if task_result['succeeded']:
		if number:
			savePath = f'./allData/output/{taskID}/{number}.wav'
		else:
			savePath = f'./allData/output/{taskID}/narakeet.wav'
		await download_to_file(task_result['result'], savePath)
	else:
		# raise Exception(task_result['message'])
		return task_result['message']
	return 200


if __name__ == '__main__':
	asyncio.run(narakeetRun("text", "你好呀", "1745737776637", "中文"))
# cur_dir = os.path.abspath(__file__).rsplit("\\", 2)[0]
# print("cur_dir", cur_dir)

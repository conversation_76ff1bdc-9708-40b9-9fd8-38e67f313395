# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .conversation_config_client_override_input import ConversationConfigClientOverrideInput
from .conversation_initiation_client_data_request_input_dynamic_variables_value import (
    ConversationInitiationClientDataRequestInputDynamicVariablesValue,
)


class ConversationInitiationClientDataRequestInput(UncheckedBaseModel):
    conversation_config_override: typing.Optional[ConversationConfigClientOverrideInput] = None
    custom_llm_extra_body: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = None
    dynamic_variables: typing.Optional[
        typing.Dict[str, typing.Optional[ConversationInitiationClientDataRequestInputDynamicVariablesValue]]
    ] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow

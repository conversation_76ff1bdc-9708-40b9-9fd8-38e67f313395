import json
import os
import time

import requests

api_key = "e6f6909926a50c8fae9a5ed24cef494916117515922ef9fbe1dfe6239c2a6321edfe6677cbca28da29be968e863614af8010907ef24b145caa822bf58dbf5801"


def SubmitTask(taskId, language):
	# Step 1: Obtain the Upload URL
	url = "https://api.tor.app/developer/transcription/local_file/get_upload_url"

	headers = {
		"Content-Type": "application/json",
		"Authorization": f"Bearer {api_key}",
		"Accept": "application/json",
	}

	# Request body with the file name
	body = json.dumps({"file_name": "separation.wav"})

	# Request to get the upload URL
	while True:
		try:
			response = requests.post(url, headers=headers, data=body)
			if response.status_code == 200:
				response_json = response.json()
				upload_url = response_json["upload_url"]
				public_url = response_json[
					"public_url"
				]  # URL to pass in initiate transcription step
				print("Upload URL obtained:", upload_url)
				print("Public URL obtained:", public_url)
			else:
				print("Failed to get upload URL:", response.status_code, response.text)
				exit()
			break
		except Exception as e:
			continue

	# Step 2: Upload the Local File
	# file_path = f"./InputAudio/{fileName}"  # Replace with your actual file path
	with open(f"./allData/output/{taskId}/separation.wav", "rb") as file_data:
		while True:
			try:
				upload_response = requests.put(upload_url, data=file_data)
				print("Upload URL obtained:", upload_response.text)
				if upload_response.status_code == 200:
					print("File uploaded successfully")
				else:
					print("File upload failed:", upload_response.status_code, upload_response.text)
					exit()
				break
			except Exception as e:
				continue

	# Step 3: Initiate Transcription for the Uploaded File
	initiate_url = (
		"https://api.tor.app/developer/transcription/local_file/initiate_transcription"
	)

	lanNames = {
		"中文": 'zh-CN',
		"英文": 'en-US',
		"高棉語": 'km-KH',
		"粵語": 'zh-HK',
		"越南語": "vi-VN",
		"日語": "ja-JP",
		"印尼語": "id-ID",
		"泰語": "th-TH",
		"阿拉伯文": "ar-XA-Wavenet-D",
		"法文": "fr-FR",
		"西班牙文": "es-ES",
		"马来文": "ms-MY",
		"韩语": "ko-KR"
	}
	# Configure transcription parameters
	config = json.dumps(
		{
			"url": public_url,  # Passing public_url to initiate transcription
			"language": lanNames[language],
			"service": "Standard",
			# "folder_id": "your_folder_id",  # Optional folder_id
			# "triggering_word": "example",  # Optional triggering_word
		}
	)

	# Send request to initiate transcription
	transcription_response = requests.post(initiate_url, headers=headers, data=config)
	print("transcription_response:", transcription_response)
	if transcription_response.status_code == 202:
		transcription_json = transcription_response.json()
		print(transcription_json["message"])
		print("Order ID:", transcription_json["order_id"])
		return transcription_json["order_id"]
	else:
		print(
			"Failed to initiate transcription:",
			transcription_response.status_code,
			transcription_response.text,
		)


def GetText(order_id):
	while True:
		try:
			# Set up the headers, including the API key
			headers = {
				'Content-Type': 'application/json',
				'Authorization': f'Bearer {api_key}',
				'Accept': 'application/json'
			}

			url = f"https://api.tor.app/developer/files/{order_id}/content"

			response = requests.get(url, headers=headers)

			response_json = response.json()
			if response_json.get("status") == "Completed":
				print(response_json)
				return response_json["content"]
			elif response_json.get("status") == "Failed":
				print("Transkriptor response data failed reason is :", response_json)
				return "failed"
			else:
				time.sleep(5)
				print("当前的状态：", response_json.get("status"))
		except Exception as e:
			print("Transkriptor GetText 出现报错啦！！！", e)


def convert_ms_to_vtt_time(milliseconds):
	"""
	将毫秒转换为 VTT 时间格式 (HH:MM:SS.mmm)

	:param milliseconds: 毫秒数 (int)
	:return: 格式化后的时间字符串 (str)
	"""
	# 计算小时、分钟、秒和毫秒
	seconds, milliseconds = divmod(milliseconds, 1000)
	minutes, seconds = divmod(seconds, 60)
	hours, minutes = divmod(minutes, 60)

	# 格式化为 HH:MM:SS.mmm
	return f"{hours:02d}:{minutes:02d}:{seconds:02d}.{milliseconds:03d}"


def RunTranskriptor(taskId, language):
	# 1、提交文件并开启转录任务
	try:
		orderId = SubmitTask(taskId, language)
	except Exception as e:
		print("Transkriptor Error：", e)
		return ""

	# 2、获取转录的内容
	try:
		content = GetText(orderId)
		print("Transkriptor content:", content)
		if content == "failed":
			print("transkriptor running retry 。。。。")
			orderId = SubmitTask(taskId, language)
			content = GetText(orderId)
			if content == "failed":
				return "failed"
	except Exception as e:
		print("Transkriptor GetText Error：", e)
		return "failed"

	responseList = []
	os.makedirs(f"./allData/output/{taskId}", exist_ok=True)
	with open(f"./allData/output/{taskId}/tran.vtt", 'w', encoding="utf-8") as f:
		f.write("WEBVTT\n\n")
		number = 1
		for one_data in content:
			f.write(
				f"{number}\n{convert_ms_to_vtt_time(one_data['StartTime'])} --> {convert_ms_to_vtt_time(one_data['EndTime'])}\n{one_data['text']}\n\n")
			responseList.append({
				'StartTime': convert_ms_to_vtt_time(one_data['StartTime']),
				'EndTime': convert_ms_to_vtt_time(one_data['EndTime']),
				'text': one_data['text'],
			})
			number += 1

	return responseList


if __name__ == '__main__':
	a = RunTranskriptor("ID17476528986171242", "印尼語")
	print("返回的内容：", a)
